{"name": "@sanity/image-url", "version": "1.1.0", "description": "Tools to generate image urls from Sanity content", "source": "src/builder.ts", "main": "lib/node/index.js", "browser": "lib/browser/image-url.umd.js", "umd:main": "lib/browser/image-url.umd.js", "typings": "lib/types/index.d.ts", "files": ["lib", "index.js", "urlForImage.js"], "sideEffects": false, "amdName": "SanityImageUrlBuilder", "scripts": {"prepublishOnly": "npm run build", "prebuild": "rimraf lib coverage .rts2*", "build": "npm run build:node && npm run build:browser", "build:node": "tsc -m commonjs", "build:browser": "microbundle build -i src/browser.ts -o lib/browser -f umd,es --no-compress", "lint": "tslint  --project tsconfig.json -t codeFrame 'src/**/*.ts' 'test/**/*.ts'", "test": "jest --coverage", "test:watch": "jest --coverage --watch", "test:prod": "npm run lint && npm run test -- --no-cache", "posttest": "npm run lint"}, "engines": {"node": ">=10.0.0"}, "jest": {"transform": {".(ts|tsx)": "ts-jest"}, "testEnvironment": "node", "testURL": "http://localhost", "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(ts|tsx|js)$", "moduleFileExtensions": ["ts", "tsx", "js"], "coveragePathIgnorePatterns": ["/node_modules/", "/test/"], "collectCoverageFrom": ["src/*.{js,ts}"]}, "prettier": {"semi": false, "singleQuote": true, "bracketSpacing": false, "printWidth": 100}, "author": "Sanity.io <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public"}, "devDependencies": {"@sanity/client": "^6.22.2", "@types/jest": "^27.4.0", "@types/node": "^17.0.18", "jest": "^27.5.1", "microbundle": "^0.15.1", "prettier": "^2.5.1", "rimraf": "^3.0.2", "ts-jest": "^27.1.3", "tslint": "^6.1.3", "tslint-config-prettier": "^1.15.0", "typescript": "^4.5.5"}, "repository": {"type": "git", "url": "git+https://github.com/sanity-io/image-url.git"}, "bugs": {"url": "https://github.com/sanity-io/image-url/issues"}, "homepage": "https://www.sanity.io/", "directories": {"test": "test"}, "keywords": ["sanity", "cms", "headless", "realtime", "content", "image-url"]}
{"name": "superagent", "version": "3.8.1", "description": "elegant & feature rich browser / node HTTP with a fluent API", "scripts": {"prepare": "make all", "test": "make test"}, "keywords": ["http", "ajax", "request", "agent"], "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> Loftis <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/visionmedia/superagent.git"}, "dependencies": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.1.1", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.0.5"}, "devDependencies": {"Base64": "^1.0.1", "basic-auth-connect": "^1.0.0", "body-parser": "^1.18.2", "browserify": "^14.1.0", "cookie-parser": "^1.4.3", "express": "^4.16.0", "express-session": "^1.15.6", "marked": "^0.3.6", "mocha": "^3.5.3", "multer": "^1.3.0", "should": "^11.2.0", "should-http": "^0.1.1", "zuul": "^3.11.1"}, "browser": {"./lib/node/index.js": "./lib/client.js", "./test/support/server.js": "./test/support/blank.js"}, "component": {"scripts": {"superagent": "lib/client.js"}}, "main": "./lib/node/index.js", "engines": {"node": ">= 4.0"}}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Logo = ({ className = \"\" }: { className?: string }) => {\n  return (\n    <div className={`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAA0B;IACtD,qBACE,8OAAC;QAAI,WAAW,CAAC,yEAAyE,EAAE,WAAW;kBACrG,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD;uCAEe", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/NewsletterSignup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/NewsletterSignup.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NewsletterSignup.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/NewsletterSignup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/NewsletterSignup.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NewsletterSignup.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/DarkModeToggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DarkModeToggles = registerClientReference(\n    function() { throw new Error(\"Attempted to call DarkModeToggles() from the server but DarkModeToggles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DarkModeToggle.tsx <module evaluation>\",\n    \"DarkModeToggles\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/DarkModeToggle.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DarkModeToggle.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mEACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/DarkModeToggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DarkModeToggles = registerClientReference(\n    function() { throw new Error(\"Attempted to call DarkModeToggles() from the server but DarkModeToggles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DarkModeToggle.tsx\",\n    \"DarkModeToggles\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/DarkModeToggle.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DarkModeToggle.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+CACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Logo from './Logo';\nimport NewsletterSignup from './NewsletterSignup';\nimport { DarkModeToggles } from './DarkModeToggle';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 dark:bg-gray-950 text-white py-12 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center mb-4\">\n              <Logo />\n              <span className=\"ml-2 text-xl font-bold\">Mobilify</span>\n            </div>\n            <p className=\"text-gray-400 text-sm leading-relaxed\">\n              Transforming ideas into mobile reality. We help entrepreneurs and businesses\n              create beautiful, high-performance mobile apps without the traditional complexity and cost.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <a href=\"/#demo\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  See Demo\n                </a>\n              </li>\n              <li>\n                <a href=\"/services\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  Services & Pricing\n                </a>\n              </li>\n              <li>\n                <a href=\"/about\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  About Us\n                </a>\n              </li>\n              <li>\n                <a href=\"/#contact\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  Contact\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Newsletter Signup */}\n          <div className=\"lg:col-span-1\">\n            <NewsletterSignup variant=\"footer\" />\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 Mobilify. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n              <DarkModeToggles.FooterToggle />\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,UAAI;;;;;sDACL,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAOvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAgE;;;;;;;;;;;sDAI7F,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAgE;;;;;;;;;;;sDAI7F,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;;;;;;;;;;;;;sCAQpG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sIAAA,CAAA,UAAgB;gCAAC,SAAQ;;;;;;;;;;;;;;;;;8BAK9B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,kBAAe,CAAC,YAAY;;;;;kDAC7B,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAwE;;;;;;kDAG9F,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5G;uCAEe", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/PortableText.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { urlFor } from '../lib/sanity';\n\n// Types for Portable Text blocks\ninterface PortableTextBlock {\n  _type: string;\n  _key: string;\n  children?: PortableTextSpan[];\n  style?: string;\n  level?: number;\n  listItem?: string;\n  markDefs?: MarkDef[];\n}\n\ninterface PortableTextSpan {\n  _type: 'span';\n  _key: string;\n  text: string;\n  marks?: string[];\n}\n\ninterface MarkDef {\n  _type: string;\n  _key: string;\n  href?: string;\n}\n\ninterface ImageBlock {\n  _type: 'image';\n  _key: string;\n  asset: {\n    _ref: string;\n  };\n  alt?: string;\n  caption?: string;\n}\n\ntype PortableTextContent = PortableTextBlock | ImageBlock;\n\ninterface PortableTextProps {\n  content: PortableTextContent[];\n  className?: string;\n}\n\nconst PortableText: React.FC<PortableTextProps> = ({ content, className = '' }) => {\n  const renderBlock = (block: PortableTextContent, index: number) => {\n    if (block._type === 'image') {\n      const imageBlock = block as ImageBlock;\n      return (\n        <div key={block._key || index} className=\"my-8\">\n          <div className=\"relative w-full h-64 md:h-96 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center\">\n            <span className=\"text-gray-500 dark:text-gray-400\">\n              {imageBlock.alt || 'Blog image'}\n            </span>\n          </div>\n          {imageBlock.caption && (\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 text-center mt-2 italic\">\n              {imageBlock.caption}\n            </p>\n          )}\n        </div>\n      );\n    }\n\n    const textBlock = block as PortableTextBlock;\n    \n    if (!textBlock.children) return null;\n\n    const renderSpan = (span: PortableTextSpan, spanIndex: number) => {\n      let text = span.text;\n      \n      if (!span.marks || span.marks.length === 0) {\n        return text;\n      }\n\n      let element = <span key={spanIndex}>{text}</span>;\n\n      span.marks.forEach((mark) => {\n        if (mark === 'strong') {\n          element = <strong key={`${spanIndex}-strong`} className=\"font-semibold\">{element}</strong>;\n        } else if (mark === 'em') {\n          element = <em key={`${spanIndex}-em`} className=\"italic\">{element}</em>;\n        } else if (mark === 'code') {\n          element = <code key={`${spanIndex}-code`} className=\"bg-gray-100 px-1 py-0.5 rounded text-sm font-mono\">{element}</code>;\n        } else {\n          // Handle link marks\n          const linkMark = textBlock.markDefs?.find(def => def._key === mark);\n          if (linkMark && linkMark._type === 'link' && linkMark.href) {\n            const isExternal = linkMark.href.startsWith('http');\n            if (isExternal) {\n              element = (\n                <a\n                  key={`${spanIndex}-link`}\n                  href={linkMark.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-electric-blue hover:underline\"\n                >\n                  {element}\n                </a>\n              );\n            } else {\n              element = (\n                <Link\n                  key={`${spanIndex}-link`}\n                  href={linkMark.href}\n                  className=\"text-electric-blue hover:underline\"\n                >\n                  {element}\n                </Link>\n              );\n            }\n          }\n        }\n      });\n\n      return element;\n    };\n\n    const children = textBlock.children.map(renderSpan);\n\n    switch (textBlock.style) {\n      case 'h1':\n        return (\n          <h1 key={textBlock._key || index} className=\"text-3xl md:text-4xl font-bold text-dark-charcoal mb-6 mt-8\">\n            {children}\n          </h1>\n        );\n      case 'h2':\n        return (\n          <h2 key={textBlock._key || index} className=\"text-2xl md:text-3xl font-bold text-dark-charcoal mb-4 mt-8\">\n            {children}\n          </h2>\n        );\n      case 'h3':\n        return (\n          <h3 key={textBlock._key || index} className=\"text-xl md:text-2xl font-semibold text-dark-charcoal mb-3 mt-6\">\n            {children}\n          </h3>\n        );\n      case 'h4':\n        return (\n          <h4 key={textBlock._key || index} className=\"text-lg md:text-xl font-semibold text-dark-charcoal mb-2 mt-4\">\n            {children}\n          </h4>\n        );\n      case 'blockquote':\n        return (\n          <blockquote key={textBlock._key || index} className=\"border-l-4 border-electric-blue pl-4 py-2 my-6 italic text-gray-700 bg-gray-50 rounded-r\">\n            {children}\n          </blockquote>\n        );\n      default:\n        if (textBlock.listItem === 'bullet') {\n          return (\n            <li key={textBlock._key || index} className=\"mb-2\">\n              {children}\n            </li>\n          );\n        }\n        if (textBlock.listItem === 'number') {\n          return (\n            <li key={textBlock._key || index} className=\"mb-2\">\n              {children}\n            </li>\n          );\n        }\n        return (\n          <p key={textBlock._key || index} className=\"mb-4 leading-relaxed text-gray-700\">\n            {children}\n          </p>\n        );\n    }\n  };\n\n  // Group list items\n  const groupedContent: (PortableTextContent | PortableTextContent[])[] = [];\n  let currentList: PortableTextContent[] = [];\n  let currentListType: string | null = null;\n\n  content.forEach((block) => {\n    if (block._type === 'block' && (block as PortableTextBlock).listItem) {\n      const listType = (block as PortableTextBlock).listItem;\n      if (listType === currentListType) {\n        currentList.push(block);\n      } else {\n        if (currentList.length > 0) {\n          groupedContent.push([...currentList]);\n        }\n        currentList = [block];\n        currentListType = listType;\n      }\n    } else {\n      if (currentList.length > 0) {\n        groupedContent.push([...currentList]);\n        currentList = [];\n        currentListType = null;\n      }\n      groupedContent.push(block);\n    }\n  });\n\n  if (currentList.length > 0) {\n    groupedContent.push([...currentList]);\n  }\n\n  return (\n    <div className={`prose prose-lg max-w-none ${className}`}>\n      {groupedContent.map((item, index) => {\n        if (Array.isArray(item)) {\n          const listType = (item[0] as PortableTextBlock).listItem;\n          const ListComponent = listType === 'bullet' ? 'ul' : 'ol';\n          return (\n            <ListComponent key={index} className={listType === 'bullet' ? 'list-disc pl-6 mb-4' : 'list-decimal pl-6 mb-4'}>\n              {item.map((listItem, listIndex) => renderBlock(listItem, listIndex))}\n            </ListComponent>\n          );\n        }\n        return renderBlock(item, index);\n      })}\n    </div>\n  );\n};\n\nexport default PortableText;\n"], "names": [], "mappings": ";;;;AACA;;;AA6CA,MAAM,eAA4C,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;IAC5E,MAAM,cAAc,CAAC,OAA4B;QAC/C,IAAI,MAAM,KAAK,KAAK,SAAS;YAC3B,MAAM,aAAa;YACnB,qBACE,8OAAC;gBAA8B,WAAU;;kCACvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACb,WAAW,GAAG,IAAI;;;;;;;;;;;oBAGtB,WAAW,OAAO,kBACjB,8OAAC;wBAAE,WAAU;kCACV,WAAW,OAAO;;;;;;;eARf,MAAM,IAAI,IAAI;;;;;QAa5B;QAEA,MAAM,YAAY;QAElB,IAAI,CAAC,UAAU,QAAQ,EAAE,OAAO;QAEhC,MAAM,aAAa,CAAC,MAAwB;YAC1C,IAAI,OAAO,KAAK,IAAI;YAEpB,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC1C,OAAO;YACT;YAEA,IAAI,wBAAU,8OAAC;0BAAsB;eAAZ;;;;;YAEzB,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;gBAClB,IAAI,SAAS,UAAU;oBACrB,wBAAU,8OAAC;wBAAmC,WAAU;kCAAiB;uBAAlD,GAAG,UAAU,OAAO,CAAC;;;;;gBAC9C,OAAO,IAAI,SAAS,MAAM;oBACxB,wBAAU,8OAAC;wBAA2B,WAAU;kCAAU;uBAAvC,GAAG,UAAU,GAAG,CAAC;;;;;gBACtC,OAAO,IAAI,SAAS,QAAQ;oBAC1B,wBAAU,8OAAC;wBAA+B,WAAU;kCAAqD;uBAApF,GAAG,UAAU,KAAK,CAAC;;;;;gBAC1C,OAAO;oBACL,oBAAoB;oBACpB,MAAM,WAAW,UAAU,QAAQ,EAAE,KAAK,CAAA,MAAO,IAAI,IAAI,KAAK;oBAC9D,IAAI,YAAY,SAAS,KAAK,KAAK,UAAU,SAAS,IAAI,EAAE;wBAC1D,MAAM,aAAa,SAAS,IAAI,CAAC,UAAU,CAAC;wBAC5C,IAAI,YAAY;4BACd,wBACE,8OAAC;gCAEC,MAAM,SAAS,IAAI;gCACnB,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAET;+BANI,GAAG,UAAU,KAAK,CAAC;;;;;wBAS9B,OAAO;4BACL,wBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,SAAS,IAAI;gCACnB,WAAU;0CAET;+BAJI,GAAG,UAAU,KAAK,CAAC;;;;;wBAO9B;oBACF;gBACF;YACF;YAEA,OAAO;QACT;QAEA,MAAM,WAAW,UAAU,QAAQ,CAAC,GAAG,CAAC;QAExC,OAAQ,UAAU,KAAK;YACrB,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAyC,WAAU;8BACjD;mBADc,UAAU,IAAI,IAAI;;;;;YAIvC;gBACE,IAAI,UAAU,QAAQ,KAAK,UAAU;oBACnC,qBACE,8OAAC;wBAAiC,WAAU;kCACzC;uBADM,UAAU,IAAI,IAAI;;;;;gBAI/B;gBACA,IAAI,UAAU,QAAQ,KAAK,UAAU;oBACnC,qBACE,8OAAC;wBAAiC,WAAU;kCACzC;uBADM,UAAU,IAAI,IAAI;;;;;gBAI/B;gBACA,qBACE,8OAAC;oBAAgC,WAAU;8BACxC;mBADK,UAAU,IAAI,IAAI;;;;;QAIhC;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAkE,EAAE;IAC1E,IAAI,cAAqC,EAAE;IAC3C,IAAI,kBAAiC;IAErC,QAAQ,OAAO,CAAC,CAAC;QACf,IAAI,MAAM,KAAK,KAAK,WAAW,AAAC,MAA4B,QAAQ,EAAE;YACpE,MAAM,WAAW,AAAC,MAA4B,QAAQ;YACtD,IAAI,aAAa,iBAAiB;gBAChC,YAAY,IAAI,CAAC;YACnB,OAAO;gBACL,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,eAAe,IAAI,CAAC;2BAAI;qBAAY;gBACtC;gBACA,cAAc;oBAAC;iBAAM;gBACrB,kBAAkB;YACpB;QACF,OAAO;YACL,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,eAAe,IAAI,CAAC;uBAAI;iBAAY;gBACpC,cAAc,EAAE;gBAChB,kBAAkB;YACpB;YACA,eAAe,IAAI,CAAC;QACtB;IACF;IAEA,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,eAAe,IAAI,CAAC;eAAI;SAAY;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0BAA0B,EAAE,WAAW;kBACrD,eAAe,GAAG,CAAC,CAAC,MAAM;YACzB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,MAAM,WAAW,AAAC,IAAI,CAAC,EAAE,CAAuB,QAAQ;gBACxD,MAAM,gBAAgB,aAAa,WAAW,OAAO;gBACrD,qBACE,8OAAC;oBAA0B,WAAW,aAAa,WAAW,wBAAwB;8BACnF,KAAK,GAAG,CAAC,CAAC,UAAU,YAAc,YAAY,UAAU;mBADvC;;;;;YAIxB;YACA,OAAO,YAAY,MAAM;QAC3B;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/blog/%5Bslug%5D/page.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Metadata } from 'next';\nimport { notFound } from 'next/navigation';\nimport { Calendar, User, ArrowLeft, ArrowRight } from 'lucide-react';\nimport Header from '../../../components/Header';\nimport Footer from '../../../components/Footer';\nimport PortableText from '../../../components/PortableText';\n\ninterface BlogPostPageProps {\n  params: Promise<{ slug: string }>;\n}\n\n// Generate metadata for SEO\nexport async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {\n  const { slug } = await params;\n\n  // For now, return sample metadata since Sanity isn't configured\n  return {\n    title: `${slug.replace(/-/g, ' ')} | Mobilify Blog`,\n    description: 'Read our latest insights on mobile app development.',\n    openGraph: {\n      title: `${slug.replace(/-/g, ' ')} | Mobilify Blog`,\n      description: 'Read our latest insights on mobile app development.',\n      type: 'article',\n    },\n  };\n}\n\nexport default async function BlogPostPage({ params }: BlogPostPageProps) {\n  const { slug } = await params;\n\n  // For now, return a sample post since Sanity isn't configured\n  const samplePost = {\n    _id: '1',\n    _type: 'post',\n    title: 'Sample Blog Post',\n    slug: { current: slug },\n    author: 'Mobilify Team',\n    mainImage: undefined,\n    categories: [\n      {\n        _id: 'mobile-dev',\n        _type: 'category',\n        title: 'Mobile Development',\n        slug: { current: 'mobile-development' },\n        description: 'Tips and tutorials for mobile app development'\n      }\n    ],\n    publishedAt: '2024-01-15T10:00:00Z',\n    excerpt: 'This is a sample blog post to demonstrate the blog functionality.',\n    body: [\n      {\n        _type: 'block',\n        _key: '1',\n        children: [\n          {\n            _type: 'span' as const,\n            _key: '1',\n            text: 'This is a sample blog post. In a real implementation, this content would come from Sanity CMS.',\n            marks: []\n          }\n        ]\n      }\n    ],\n    _createdAt: '2024-01-15T10:00:00Z',\n    _updatedAt: '2024-01-15T10:00:00Z'\n  };\n\n  const post = samplePost;\n\n  if (!post) {\n    notFound();\n  }\n\n  // For now, use empty related posts since Sanity isn't configured\n  const relatedPosts: any[] = [];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  };\n\n  // Generate JSON-LD structured data\n  const jsonLd = {\n    '@context': 'https://schema.org',\n    '@type': 'Article',\n    headline: post.title,\n    description: post.excerpt,\n    image: '/placeholder-image.jpg',\n    datePublished: post.publishedAt,\n    dateModified: post._updatedAt,\n    author: {\n      '@type': 'Person',\n      name: post.author,\n    },\n    publisher: {\n      '@type': 'Organization',\n      name: 'Mobilify',\n      logo: {\n        '@type': 'ImageObject',\n        url: '/logo.png',\n      },\n    },\n    mainEntityOfPage: {\n      '@type': 'WebPage',\n      '@id': `https://mobilify.com/blog/${post.slug.current}`,\n    },\n  };\n\n  return (\n    <>\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}\n      />\n      \n      <div className=\"min-h-screen w-full overflow-x-hidden\">\n        <Header />\n        \n        <main className=\"pt-16\">\n          {/* Back to Blog */}\n          <section className=\"py-6 bg-gray-50\">\n            <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <Link\n                href=\"/blog\"\n                className=\"inline-flex items-center text-electric-blue hover:underline font-medium\"\n              >\n                <ArrowLeft className=\"mr-2 w-4 h-4\" />\n                Back to Blog\n              </Link>\n            </div>\n          </section>\n\n          {/* Article Header */}\n          <article className=\"py-12\">\n            <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {/* Categories */}\n              <div className=\"flex flex-wrap gap-2 mb-6\">\n                {post.categories.map((category) => (\n                  <Link\n                    key={category._id}\n                    href={`/blog?category=${category.slug.current}`}\n                    className=\"text-sm font-medium text-electric-blue bg-blue-50 px-3 py-1 rounded-full hover:bg-blue-100 transition-colors duration-200\"\n                  >\n                    {category.title}\n                  </Link>\n                ))}\n              </div>\n\n              {/* Title */}\n              <h1 className=\"text-4xl md:text-5xl font-bold text-dark-charcoal mb-6 leading-tight\">\n                {post.title}\n              </h1>\n\n              {/* Excerpt */}\n              {post.excerpt && (\n                <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n                  {post.excerpt}\n                </p>\n              )}\n\n              {/* Meta */}\n              <div className=\"flex items-center gap-6 text-gray-500 mb-8 pb-8 border-b\">\n                <div className=\"flex items-center gap-2\">\n                  <User className=\"w-5 h-5\" />\n                  <span className=\"font-medium\">{post.author}</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Calendar className=\"w-5 h-5\" />\n                  <span>{formatDate(post.publishedAt)}</span>\n                </div>\n              </div>\n\n              {/* Featured Image - placeholder for now */}\n              <div className=\"relative w-full h-64 md:h-96 rounded-xl overflow-hidden mb-12 bg-gray-200 dark:bg-gray-700 flex items-center justify-center\">\n                <span className=\"text-gray-500 dark:text-gray-400\">Sample Blog Post Image</span>\n              </div>\n\n              {/* Article Content */}\n              <div className=\"prose prose-lg max-w-none\">\n                <PortableText content={post.body} />\n              </div>\n            </div>\n          </article>\n\n          {/* Related Posts */}\n          {relatedPosts.length > 0 && (\n            <section className=\"py-16 bg-gray-50\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <h2 className=\"text-3xl font-bold text-dark-charcoal mb-8 text-center\">\n                  Related Articles\n                </h2>\n                \n                <div className=\"grid md:grid-cols-3 gap-8\">\n                  {relatedPosts.map((relatedPost) => (\n                    <article\n                      key={relatedPost._id}\n                      className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden\"\n                    >\n                      <div className=\"relative h-48 w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center\">\n                        <span className=\"text-gray-500 dark:text-gray-400 text-sm\">Related Post Image</span>\n                      </div>\n                      \n                      <div className=\"p-6\">\n                        <h3 className=\"text-lg font-bold text-dark-charcoal mb-2 line-clamp-2\">\n                          <Link\n                            href={`/blog/${relatedPost.slug.current}`}\n                            className=\"hover:text-electric-blue transition-colors duration-200\"\n                          >\n                            {relatedPost.title}\n                          </Link>\n                        </h3>\n                        \n                        {relatedPost.excerpt && (\n                          <p className=\"text-gray-600 mb-4 line-clamp-3 text-sm\">\n                            {relatedPost.excerpt}\n                          </p>\n                        )}\n                        \n                        <div className=\"flex items-center justify-between\">\n                          <span className=\"text-sm text-gray-500\">\n                            {formatDate(relatedPost.publishedAt)}\n                          </span>\n                          <Link\n                            href={`/blog/${relatedPost.slug.current}`}\n                            className=\"inline-flex items-center text-electric-blue font-medium hover:underline text-sm\"\n                          >\n                            Read More\n                            <ArrowRight className=\"ml-1 w-3 h-3\" />\n                          </Link>\n                        </div>\n                      </div>\n                    </article>\n                  ))}\n                </div>\n              </div>\n            </section>\n          )}\n        </main>\n\n        <Footer />\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AAOO,eAAe,iBAAiB,EAAE,MAAM,EAAqB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,gEAAgE;IAChE,OAAO;QACL,OAAO,GAAG,KAAK,OAAO,CAAC,MAAM,KAAK,gBAAgB,CAAC;QACnD,aAAa;QACb,WAAW;YACT,OAAO,GAAG,KAAK,OAAO,CAAC,MAAM,KAAK,gBAAgB,CAAC;YACnD,aAAa;YACb,MAAM;QACR;IACF;AACF;AAEe,eAAe,aAAa,EAAE,MAAM,EAAqB;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,8DAA8D;IAC9D,MAAM,aAAa;QACjB,KAAK;QACL,OAAO;QACP,OAAO;QACP,MAAM;YAAE,SAAS;QAAK;QACtB,QAAQ;QACR,WAAW;QACX,YAAY;YACV;gBACE,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,MAAM;oBAAE,SAAS;gBAAqB;gBACtC,aAAa;YACf;SACD;QACD,aAAa;QACb,SAAS;QACT,MAAM;YACJ;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;oBACR;wBACE,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,OAAO,EAAE;oBACX;iBACD;YACH;SACD;QACD,YAAY;QACZ,YAAY;IACd;IAEA,MAAM,OAAO;IAEb,uCAAW;;IAEX;IAEA,iEAAiE;IACjE,MAAM,eAAsB,EAAE;IAE9B,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,mCAAmC;IACnC,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,UAAU,KAAK,KAAK;QACpB,aAAa,KAAK,OAAO;QACzB,OAAO;QACP,eAAe,KAAK,WAAW;QAC/B,cAAc,KAAK,UAAU;QAC7B,QAAQ;YACN,SAAS;YACT,MAAM,KAAK,MAAM;QACnB;QACA,WAAW;YACT,SAAS;YACT,MAAM;YACN,MAAM;gBACJ,SAAS;gBACT,KAAK;YACP;QACF;QACA,kBAAkB;YAChB,SAAS;YACT,OAAO,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE;QACzD;IACF;IAEA,qBACE;;0BACE,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBAAE,QAAQ,KAAK,SAAS,CAAC;gBAAQ;;;;;;0BAG5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,UAAM;;;;;kCAEP,8OAAC;wBAAK,WAAU;;0CAEd,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;0CAO5C,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,yBACpB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,CAAC,eAAe,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE;oDAC/C,WAAU;8DAET,SAAS,KAAK;mDAJV,SAAS,GAAG;;;;;;;;;;sDAUvB,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;wCAIZ,KAAK,OAAO,kBACX,8OAAC;4CAAE,WAAU;sDACV,KAAK,OAAO;;;;;;sDAKjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAe,KAAK,MAAM;;;;;;;;;;;;8DAE5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;;;;;;;sDAKtC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;;;;;;sDAIrD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;gDAAC,SAAS,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;4BAMrC,aAAa,MAAM,GAAG,mBACrB,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyD;;;;;;sDAIvE,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA2C;;;;;;;;;;;sEAG7D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,MAAM,EAAE,YAAY,IAAI,CAAC,OAAO,EAAE;wEACzC,WAAU;kFAET,YAAY,KAAK;;;;;;;;;;;gEAIrB,YAAY,OAAO,kBAClB,8OAAC;oEAAE,WAAU;8EACV,YAAY,OAAO;;;;;;8EAIxB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACb,WAAW,YAAY,WAAW;;;;;;sFAErC,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAM,CAAC,MAAM,EAAE,YAAY,IAAI,CAAC,OAAO,EAAE;4EACzC,WAAU;;gFACX;8FAEC,8OAAC,kNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;mDAhCvB,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA4ClC,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;;AAIf", "debugId": null}}]}
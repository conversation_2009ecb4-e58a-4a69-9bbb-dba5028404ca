(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/GoogleAnalytics.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
const GoogleAnalytics = ()=>{
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleAnalytics.useEffect": ()=>{
            // Only load Google Analytics in production and if GA_ID is provided
            const GA_ID = ("TURBOPACK compile-time value", "");
            if ("TURBOPACK compile-time truthy", 1) {
                return;
            }
            "TURBOPACK unreachable";
            // Load Google Analytics script
            const script1 = undefined;
            const script2 = undefined;
        }
    }["GoogleAnalytics.useEffect"], []);
    return null;
};
_s(GoogleAnalytics, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = GoogleAnalytics;
const __TURBOPACK__default__export__ = GoogleAnalytics;
var _c;
__turbopack_context__.k.register(_c, "GoogleAnalytics");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/CrispChat.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "crispUtils": (()=>crispUtils),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
const CrispChat = ({ websiteId })=>{
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CrispChat.useEffect": ()=>{
            // Get website ID from props or environment variable
            const crispWebsiteId = websiteId || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_CRISP_WEBSITE_ID;
            if (!crispWebsiteId) {
                console.warn('Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable.');
                return;
            }
            // Initialize Crisp if not already loaded
            if ("object" !== 'undefined' && !window.$crisp) {
                window.$crisp = [];
                window.CRISP_WEBSITE_ID = crispWebsiteId;
                // Create and append the Crisp script
                const script = document.createElement('script');
                script.src = 'https://client.crisp.chat/l.js';
                script.async = true;
                document.getElementsByTagName('head')[0].appendChild(script);
                // Configure Crisp settings
                window.$crisp.push([
                    'safe',
                    true
                ]);
                // Set custom data for better support
                window.$crisp.push([
                    'set',
                    'user:company',
                    'Mobilify Website Visitor'
                ]);
                window.$crisp.push([
                    'set',
                    'session:data',
                    {
                        source: 'website',
                        page: window.location.pathname,
                        timestamp: new Date().toISOString()
                    }
                ]);
                // Track chat interactions for analytics
                window.$crisp.push([
                    'on',
                    'chat:opened',
                    {
                        "CrispChat.useEffect": ()=>{
                            if ("object" !== 'undefined' && window.gtag) {
                                window.gtag('event', 'chat_opened', {
                                    event_category: 'engagement',
                                    event_label: 'crisp_chat'
                                });
                            }
                        }
                    }["CrispChat.useEffect"]
                ]);
                window.$crisp.push([
                    'on',
                    'message:sent',
                    {
                        "CrispChat.useEffect": ()=>{
                            if ("object" !== 'undefined' && window.gtag) {
                                window.gtag('event', 'chat_message_sent', {
                                    event_category: 'engagement',
                                    event_label: 'crisp_chat'
                                });
                            }
                        }
                    }["CrispChat.useEffect"]
                ]);
                window.$crisp.push([
                    'on',
                    'message:received',
                    {
                        "CrispChat.useEffect": ()=>{
                            if ("object" !== 'undefined' && window.gtag) {
                                window.gtag('event', 'chat_message_received', {
                                    event_category: 'engagement',
                                    event_label: 'crisp_chat'
                                });
                            }
                        }
                    }["CrispChat.useEffect"]
                ]);
            }
            // Cleanup function
            return ({
                "CrispChat.useEffect": ()=>{
                // Note: Crisp doesn't provide a clean way to remove itself
                // This is intentional as chat sessions should persist across page navigation
                }
            })["CrispChat.useEffect"];
        }
    }["CrispChat.useEffect"], [
        websiteId
    ]);
    // This component doesn't render anything visible
    // The chat widget is injected by Crisp's script
    return null;
};
_s(CrispChat, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = CrispChat;
const __TURBOPACK__default__export__ = CrispChat;
const crispUtils = {
    // Open the chat widget
    openChat: ()=>{
        if ("object" !== 'undefined' && window.$crisp) {
            window.$crisp.push([
                'do',
                'chat:open'
            ]);
        }
    },
    // Close the chat widget
    closeChat: ()=>{
        if ("object" !== 'undefined' && window.$crisp) {
            window.$crisp.push([
                'do',
                'chat:close'
            ]);
        }
    },
    // Show the chat widget
    showChat: ()=>{
        if ("object" !== 'undefined' && window.$crisp) {
            window.$crisp.push([
                'do',
                'chat:show'
            ]);
        }
    },
    // Hide the chat widget
    hideChat: ()=>{
        if ("object" !== 'undefined' && window.$crisp) {
            window.$crisp.push([
                'do',
                'chat:hide'
            ]);
        }
    },
    // Set user information
    setUser: (user)=>{
        if ("object" !== 'undefined' && window.$crisp) {
            if (user.nickname) window.$crisp.push([
                'set',
                'user:nickname',
                user.nickname
            ]);
            if (user.email) window.$crisp.push([
                'set',
                'user:email',
                user.email
            ]);
            if (user.phone) window.$crisp.push([
                'set',
                'user:phone',
                user.phone
            ]);
            if (user.avatar) window.$crisp.push([
                'set',
                'user:avatar',
                user.avatar
            ]);
        }
    },
    // Send a message programmatically
    sendMessage: (message)=>{
        if ("object" !== 'undefined' && window.$crisp) {
            window.$crisp.push([
                'do',
                'message:send',
                [
                    'text',
                    message
                ]
            ]);
        }
    },
    // Set session data
    setSessionData: (data)=>{
        if ("object" !== 'undefined' && window.$crisp) {
            window.$crisp.push([
                'set',
                'session:data',
                data
            ]);
        }
    },
    // Check if chat is available
    isChatAvailable: ()=>{
        if ("object" !== 'undefined' && window.$crisp) {
            return window.$crisp.length > 0;
        }
        return false;
    },
    // Set custom segments for targeting
    setSegments: (segments)=>{
        if ("object" !== 'undefined' && window.$crisp) {
            window.$crisp.push([
                'set',
                'session:segments',
                segments
            ]);
        }
    }
};
var _c;
__turbopack_context__.k.register(_c, "CrispChat");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ThemeContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "getThemeClasses": (()=>getThemeClasses),
    "useSystemTheme": (()=>useSystemTheme),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useTheme = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};
_s(useTheme, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const ThemeProvider = ({ children })=>{
    _s1();
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Initialize theme from localStorage or system preference
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            const savedTheme = localStorage.getItem('mobilify-theme');
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            const initialTheme = savedTheme || systemTheme;
            setThemeState(initialTheme);
            setMounted(true);
        }
    }["ThemeProvider.useEffect"], []);
    // Apply theme to document
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            const root = document.documentElement;
            if (theme === 'dark') {
                root.classList.add('dark');
            } else {
                root.classList.remove('dark');
            }
            // Save to localStorage
            localStorage.setItem('mobilify-theme', theme);
            // Track theme change for analytics
            if ("object" !== 'undefined' && window.gtag) {
                window.gtag('event', 'theme_changed', {
                    event_category: 'user_preference',
                    event_label: theme
                });
            }
        }
    }["ThemeProvider.useEffect"], [
        theme,
        mounted
    ]);
    // Listen for system theme changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const handleChange = {
                "ThemeProvider.useEffect.handleChange": (e)=>{
                    // Only auto-switch if user hasn't manually set a preference
                    const savedTheme = localStorage.getItem('mobilify-theme');
                    if (!savedTheme) {
                        setThemeState(e.matches ? 'dark' : 'light');
                    }
                }
            }["ThemeProvider.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "ThemeProvider.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["ThemeProvider.useEffect"];
        }
    }["ThemeProvider.useEffect"], [
        mounted
    ]);
    const toggleTheme = ()=>{
        setThemeState((prevTheme)=>prevTheme === 'light' ? 'dark' : 'light');
    };
    const setTheme = (newTheme)=>{
        setThemeState(newTheme);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: {
            theme,
            toggleTheme,
            setTheme
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            suppressHydrationWarning: true,
            children: children
        }, void 0, false, {
            fileName: "[project]/src/contexts/ThemeContext.tsx",
            lineNumber: 93,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/contexts/ThemeContext.tsx",
        lineNumber: 92,
        columnNumber: 5
    }, this);
};
_s1(ThemeProvider, "***************************=");
_c = ThemeProvider;
const useSystemTheme = ()=>{
    _s2();
    const [systemTheme, setSystemTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSystemTheme.useEffect": ()=>{
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
            const handleChange = {
                "useSystemTheme.useEffect.handleChange": (e)=>{
                    setSystemTheme(e.matches ? 'dark' : 'light');
                }
            }["useSystemTheme.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "useSystemTheme.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["useSystemTheme.useEffect"];
        }
    }["useSystemTheme.useEffect"], []);
    return systemTheme;
};
_s2(useSystemTheme, "qhNRPuY27tvw2LahbgnRY0uG6pE=");
const getThemeClasses = (lightClasses, darkClasses)=>{
    return `${lightClasses} dark:${darkClasses}`;
};
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/NoSSR.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
const NoSSR = ({ children, fallback = null })=>{
    _s();
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NoSSR.useEffect": ()=>{
            setMounted(true);
        }
    }["NoSSR.useEffect"], []);
    if (!mounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: fallback
        }, void 0, false);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
};
_s(NoSSR, "LrrVfNW3d1raFE0BNzCTILYmIfo=");
_c = NoSSR;
const __TURBOPACK__default__export__ = NoSSR;
var _c;
__turbopack_context__.k.register(_c, "NoSSR");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_2901751f._.js.map
(()=>{var e={};e.id=953,e.ids=[953],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return s}});let s=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2268:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23)),Promise.resolve().then(r.bind(r,38435)),Promise.resolve().then(r.bind(r,24597)),Promise.resolve().then(r.bind(r,68367))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18512:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(37413);r(61120);let i=({className:e=""})=>(0,s.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})});var l=r(68367),n=r(38435);let o=()=>(0,s.jsx)("footer",{className:"bg-gray-900 dark:bg-gray-950 text-white py-12 transition-colors duration-300",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-1",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(i,{}),(0,s.jsx)("span",{className:"ml-2 text-xl font-bold",children:"Mobilify"})]}),(0,s.jsx)("p",{className:"text-gray-400 text-sm leading-relaxed",children:"Transforming ideas into mobile reality. We help entrepreneurs and businesses create beautiful, high-performance mobile apps without the traditional complexity and cost."})]}),(0,s.jsxs)("div",{className:"lg:col-span-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/#demo",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"See Demo"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/services",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"Services & Pricing"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/about",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"About Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/#contact",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"Contact"})})]})]}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(l.default,{variant:"footer"})})]}),(0,s.jsx)("div",{className:"border-t border-gray-800 pt-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 Mobilify. All rights reserved."}),(0,s.jsxs)("div",{className:"flex items-center space-x-6 mt-4 md:mt-0",children:[(0,s.jsx)(n.DarkModeToggles.FooterToggle,{}),(0,s.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors duration-200",children:"Privacy Policy"}),(0,s.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors duration-200",children:"Terms of Service"})]})]})})]})})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24597:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Header.tsx","default")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38435:(e,t,r)=>{"use strict";r.d(t,{DarkModeToggles:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\DarkModeToggle.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx","default");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call DarkModeToggles() from the server but DarkModeToggles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx","DarkModeToggles")},44012:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),i=r(48088),l=r(88170),n=r.n(l),o=r(30893),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);r.d(t,a);let d={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83127)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48976:(e,t,r)=>{"use strict";function s(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return s}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60412:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23)),Promise.resolve().then(r.bind(r,52797)),Promise.resolve().then(r.bind(r,35899)),Promise.resolve().then(r.bind(r,91477))},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let s=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(s),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=s,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68367:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\NewsletterSignup.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx","default")},70899:(e,t,r)=>{"use strict";function s(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return s}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,n.isNextRouterError)(t)||(0,l.isBailoutToCSRError)(t)||(0,a.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,s.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let s=r(68388),i=r(52637),l=r(51846),n=r(31162),o=r(84971),a=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83127:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b,generateMetadata:()=>x});var s=r(37413);r(61120);var i=r(4536),l=r.n(i),n=r(53384),o=r(97576);let a=(0,r(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var d=r(88971),c=r(40918),u=r(36440),m=r(24597),p=r(18512),f=r(97569);let h=({content:e,className:t=""})=>{let r=(e,t)=>{if("image"===e._type)return(0,s.jsxs)("div",{className:"my-8",children:[(0,s.jsx)("div",{className:"relative w-full h-64 md:h-96 rounded-lg overflow-hidden",children:(0,s.jsx)(n.default,{src:(0,f.dk)(e).width(800).height(400).url(),alt:e.alt||"Blog image",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"})}),e.caption&&(0,s.jsx)("p",{className:"text-sm text-gray-600 text-center mt-2 italic",children:e.caption})]},e._key||t);if(!e.children)return null;let r=e.children.map((t,r)=>{let i=t.text;if(!t.marks||0===t.marks.length)return i;let n=(0,s.jsx)("span",{children:i},r);return t.marks.forEach(t=>{if("strong"===t)n=(0,s.jsx)("strong",{className:"font-semibold",children:n},`${r}-strong`);else if("em"===t)n=(0,s.jsx)("em",{className:"italic",children:n},`${r}-em`);else if("code"===t)n=(0,s.jsx)("code",{className:"bg-gray-100 px-1 py-0.5 rounded text-sm font-mono",children:n},`${r}-code`);else{let i=e.markDefs?.find(e=>e._key===t);i&&"link"===i._type&&i.href&&(n=i.href.startsWith("http")?(0,s.jsx)("a",{href:i.href,target:"_blank",rel:"noopener noreferrer",className:"text-electric-blue hover:underline",children:n},`${r}-link`):(0,s.jsx)(l(),{href:i.href,className:"text-electric-blue hover:underline",children:n},`${r}-link`))}}),n});switch(e.style){case"h1":return(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-dark-charcoal mb-6 mt-8",children:r},e._key||t);case"h2":return(0,s.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-dark-charcoal mb-4 mt-8",children:r},e._key||t);case"h3":return(0,s.jsx)("h3",{className:"text-xl md:text-2xl font-semibold text-dark-charcoal mb-3 mt-6",children:r},e._key||t);case"h4":return(0,s.jsx)("h4",{className:"text-lg md:text-xl font-semibold text-dark-charcoal mb-2 mt-4",children:r},e._key||t);case"blockquote":return(0,s.jsx)("blockquote",{className:"border-l-4 border-electric-blue pl-4 py-2 my-6 italic text-gray-700 bg-gray-50 rounded-r",children:r},e._key||t);default:if("bullet"===e.listItem||"number"===e.listItem)return(0,s.jsx)("li",{className:"mb-2",children:r},e._key||t);return(0,s.jsx)("p",{className:"mb-4 leading-relaxed text-gray-700",children:r},e._key||t)}},i=[],o=[],a=null;return e.forEach(e=>{if("block"===e._type&&e.listItem){let t=e.listItem;t===a?o.push(e):(o.length>0&&i.push([...o]),o=[e],a=t)}else o.length>0&&(i.push([...o]),o=[],a=null),i.push(e)}),o.length>0&&i.push([...o]),(0,s.jsx)("div",{className:`prose prose-lg max-w-none ${t}`,children:i.map((e,t)=>{if(Array.isArray(e)){let i=e[0].listItem;return(0,s.jsx)("bullet"===i?"ul":"ol",{className:"bullet"===i?"list-disc pl-6 mb-4":"list-decimal pl-6 mb-4",children:e.map((e,t)=>r(e,t))},t)}return r(e,t)})})};async function x({params:e}){let t=await (0,f.N7)(e.slug);if(!t)return{title:"Post Not Found | Mobilify"};let r=t.mainImage?(0,f.dk)(t.mainImage).width(1200).height(630).url():"/og-default.jpg";return{title:`${t.title} | Mobilify Blog`,description:t.excerpt||`Read about ${t.title} on the Mobilify blog.`,openGraph:{title:t.title,description:t.excerpt||`Read about ${t.title} on the Mobilify blog.`,type:"article",publishedTime:t.publishedAt,authors:[t.author],images:[{url:r,width:1200,height:630,alt:t.title}]},twitter:{card:"summary_large_image",title:t.title,description:t.excerpt||`Read about ${t.title} on the Mobilify blog.`,images:[r]}}}async function b({params:e}){let t=await (0,f.N7)(e.slug);t||(0,o.notFound)();let r=t.categories.map(e=>e._id),i=await (0,f.x8)(t._id,r),x=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),b={"@context":"https://schema.org","@type":"Article",headline:t.title,description:t.excerpt,image:t.mainImage?(0,f.dk)(t.mainImage).width(1200).height(630).url():void 0,datePublished:t.publishedAt,dateModified:t._updatedAt,author:{"@type":"Person",name:t.author},publisher:{"@type":"Organization",name:"Mobilify",logo:{"@type":"ImageObject",url:"/logo.png"}},mainEntityOfPage:{"@type":"WebPage","@id":`https://mobilify.com/blog/${t.slug.current}`}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(b)}}),(0,s.jsxs)("div",{className:"min-h-screen w-full overflow-x-hidden",children:[(0,s.jsx)(m.default,{}),(0,s.jsxs)("main",{className:"pt-16",children:[(0,s.jsx)("section",{className:"py-6 bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)(l(),{href:"/blog",className:"inline-flex items-center text-electric-blue hover:underline font-medium",children:[(0,s.jsx)(a,{className:"mr-2 w-4 h-4"}),"Back to Blog"]})})}),(0,s.jsx)("article",{className:"py-12",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:t.categories.map(e=>(0,s.jsx)(l(),{href:`/blog?category=${e.slug.current}`,className:"text-sm font-medium text-electric-blue bg-blue-50 px-3 py-1 rounded-full hover:bg-blue-100 transition-colors duration-200",children:e.title},e._id))}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-dark-charcoal mb-6 leading-tight",children:t.title}),t.excerpt&&(0,s.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:t.excerpt}),(0,s.jsxs)("div",{className:"flex items-center gap-6 text-gray-500 mb-8 pb-8 border-b",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{className:"font-medium",children:t.author})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:x(t.publishedAt)})]})]}),t.mainImage&&(0,s.jsx)("div",{className:"relative w-full h-64 md:h-96 rounded-xl overflow-hidden mb-12",children:(0,s.jsx)(n.default,{src:(0,f.dk)(t.mainImage).width(1200).height(600).url(),alt:t.mainImage.alt||t.title,fill:!0,className:"object-cover",priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"})}),(0,s.jsx)("div",{className:"prose prose-lg max-w-none",children:(0,s.jsx)(h,{content:t.body})})]})}),i.length>0&&(0,s.jsx)("section",{className:"py-16 bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-dark-charcoal mb-8 text-center",children:"Related Articles"}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:i.map(e=>(0,s.jsxs)("article",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden",children:[e.mainImage&&(0,s.jsx)("div",{className:"relative h-48 w-full",children:(0,s.jsx)(n.default,{src:(0,f.dk)(e.mainImage).width(400).height(200).url(),alt:e.mainImage.alt||e.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-dark-charcoal mb-2 line-clamp-2",children:(0,s.jsx)(l(),{href:`/blog/${e.slug.current}`,className:"hover:text-electric-blue transition-colors duration-200",children:e.title})}),e.excerpt&&(0,s.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3 text-sm",children:e.excerpt}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:x(e.publishedAt)}),(0,s.jsxs)(l(),{href:`/blog/${e.slug.current}`,className:"inline-flex items-center text-electric-blue font-medium hover:underline text-sm",children:["Read More",(0,s.jsx)(u.A,{className:"ml-1 w-3 h-3"})]})]})]})]},e._id))})]})})]}),(0,s.jsx)(p.A,{})]})]})}},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return n},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return a},redirect:function(){return o}});let s=r(52836),i=r(49026),l=r(19121).actionAsyncStorage;function n(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let l=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return l.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",l}function o(e,t){var r;throw null!=t||(t=(null==l||null==(r=l.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),n(e,t,s.RedirectStatusCode.TemporaryRedirect)}function a(e,t){throw void 0===t&&(t=i.RedirectType.replace),n(e,t,s.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94735:e=>{"use strict";e.exports=require("events")},97569:(e,t,r)=>{"use strict";r.d(t,{N7:()=>m,QU:()=>p,dk:()=>n,x8:()=>f,zX:()=>u});var s=r(25588);!function(){var e=Error("Cannot find module '@sanity/image-url'");throw e.code="MODULE_NOT_FOUND",e}();let i=(0,s.UU)({projectId:process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,dataset:process.env.NEXT_PUBLIC_SANITY_DATASET||"production",apiVersion:"2024-01-01",useCdn:!0,token:process.env.SANITY_API_TOKEN}),l=Object(function(){var e=Error("Cannot find module '@sanity/image-url'");throw e.code="MODULE_NOT_FOUND",e}())(i);function n(e){return l.image(e)}let o=`*[_type == "post" && defined(slug.current)] | order(publishedAt desc) {
  _id,
  title,
  slug,
  author,
  mainImage,
  categories[]-> {
    _id,
    title,
    slug
  },
  publishedAt,
  excerpt,
  _createdAt
}`,a=`*[_type == "post" && slug.current == $slug][0] {
  _id,
  title,
  slug,
  author,
  mainImage,
  categories[]-> {
    _id,
    title,
    slug,
    description
  },
  publishedAt,
  excerpt,
  body,
  _createdAt,
  _updatedAt
}`,d=`*[_type == "category"] | order(title asc) {
  _id,
  title,
  slug,
  description
}`,c=`*[_type == "post" && _id != $postId && count(categories[@._ref in $categoryIds]) > 0] | order(publishedAt desc)[0...3] {
  _id,
  title,
  slug,
  mainImage,
  publishedAt,
  excerpt
}`;async function u(){return await i.fetch(o)}async function m(e){return await i.fetch(a,{slug:e})}async function p(){return await i.fetch(d)}async function f(e,t){return await i.fetch(c,{postId:e,categoryIds:t})}},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return i.RedirectType},forbidden:function(){return n.forbidden},notFound:function(){return l.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return a.unstable_rethrow}});let s=r(86897),i=r(49026),l=r(62765),n=r(48976),o=r(70899),a=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,71,276,814,588,533,331,225,935],()=>r(44012));module.exports=s})();
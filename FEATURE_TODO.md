# 🚀 Mobilify Website – Feature & Improvement TODO List

> **Note:** Mobilify is a new startup with no previous clients. Features like testimonials, portfolio, and case studies can be planned for the future as the business grows.

## **PRIORITY 1: Core Business Features**

### 1. Interactive "Website-to-App" Demo Enhancement 🎯
**Priority: HIGHEST - This is the "Aha!" moment for users**
**Status: ✅ COMPLETED**
- [x] Enhance existing `InteractiveDemo.tsx` component with full UI implementation
- [x] Build animated phone mockup with "fade-in and slide-up" animation
- [x] Implement dark-themed dashboard preview as specified in design plan
- [x] Add analytics tracking for:
  - `demo_interaction` (main button click)
  - `tab_switch` (toggle between "Convert Website" and "Describe Idea")
  - `demo_animation_complete` (when phone mockup finishes animation)
- [x] Follow design specifications from MobilifyWebsiteStructure&VibeCodingPlanbyGemini.md
- [x] Update `.env.local.example` with new environment variables needed
- [x] Update semantic color usage (electric-blue for charts, dark-charcoal for text)

### 2. Newsletter Signup 📧
**Priority: HIGH - Crucial low-effort lead capture tool**
**Status: ✅ COMPLETED**
- [x] Add newsletter signup form in website footer
- [x] Add prominent newsletter section on homepage
- [x] Integrate with Mailchimp API with proper error handling
- [x] Configure via environment variables (`MAILCHIMP_API_KEY`, `MAILCHIMP_LIST_ID`)
- [x] Add form validation and loading states with visual feedback
- [x] Implement analytics tracking for newsletter signups
- [x] Test responsive design across all devices
- [x] Add proper TypeScript types and error handling

### 3. Blog & FAQ Sections 📝
**Priority: HIGH - Builds authority and improves SEO**
**Status: IN PROGRESS**
- [x] **Decision:** Use Sanity's hosted platform for the CMS Studio.
- [x] **Decision:** Create schemas for both Blog (with categories) and FAQ (with topics), with cross-linking.
- [ ] **CMS Setup:**
  - [ ] Initialize Sanity project and define schemas for:
    - `post` (with fields for title, slug, author, mainImage, categories, body)
    - `category` (for blog posts)
    - `faqItem` (with fields for question, answer, topic)
    - `faqTopic` (for FAQs)
  - [ ] Configure Sanity project with environment variables in `.env.local`.
  - [ ] Create sample content: 2-3 blog posts and 4-5 FAQ items for demonstration.
- [ ] **Frontend Implementation:**
  - [ ] Create blog list page (`/blog`) and single post layout (`/blog/[slug]`).
  - [ ] Create dedicated FAQ page (`/faq`) that groups questions by topic.
  - [ ] Implement utility functions to fetch data from Sanity.
- [ ] **SEO Implementation:**
  - [ ] Dynamically generate `sitemap.xml` to include all blog posts.
  - [ ] Add `Article` Schema.org markup to blog post pages.
  - [ ] Add `FAQPage` Schema.org markup to the FAQ page.
  - [ ] Implement dynamic OpenGraph tags (title, description, image) for each blog post.

### 4. Live Chat Support 💬
**Priority: MEDIUM - Increases engagement and lead capture**
- [ ] Implement Tawk.to or Crisp (generous free tiers)
- [ ] Add chat script to `layout.tsx` via environment variable (e.g., `NEXT_PUBLIC_CRISP_WEBSITE_ID`)
- [ ] Test chat widget on all devices and screen sizes
- [ ] Configure chat settings for business hours and auto-responses

## **PRIORITY 2: User Experience Polish**

### 5. Dark Mode Toggle 🌙
**Priority: MEDIUM - Great "polish" feature for tech-savvy vibe**
- [ ] Add dark mode toggle in header (left of "Get a Quote" button)
- [ ] Use sun/moon icon-based toggle design
- [ ] Update Tailwind config with `darkMode: 'class'`
- [ ] Persist preference in localStorage
- [ ] Test all pages/components in both light and dark modes
- [ ] Ensure proper color contrast in both modes

## **DEFERRED FEATURES**
> These features are explicitly deferred based on MVP requirements

### ~~Multi-language Support (i18n)~~ ❌
**Status: DEFERRED - No localization for MVP as per project plan**
- This feature will be reconsidered post-MVP when international expansion is planned

### ~~Custom Admin Dashboard~~ ❌
**Status: SIMPLIFIED - Using Sanity CMS interface instead**
- Authentication and content management will be handled entirely through Sanity's interface
- No custom `/admin` routes needed for MVP

## **PRIORITY 3: Technical Foundation & Quality**

### 6. Design System Consistency 🎨
**Priority: HIGH - Foundation for all other features**
- [ ] Update `tailwind.config.js` with semantic color names:
  - `dark-charcoal: '#111827'` (primary text)
  - `electric-blue: '#4f46e5'` (accent/primary action)
- [ ] Refactor `ServicesOverview.tsx` to use semantic color classes instead of hardcoded values
- [ ] Apply consistent color usage across all components
- [ ] Ensure design follows MobilifyWebsiteStructure&VibeCodingPlanbyGemini.md specifications

### 7. Testing & Quality Assurance 🧪
**Priority: MEDIUM - Essential for maintainable codebase**
- [ ] Set up Jest + React Testing Library for unit tests
- [ ] Write tests for critical components (Header, Contact form, InteractiveDemo)
- [ ] Set up Storybook for component development and documentation
- [ ] Add E2E tests with Playwright for key user flows
- [ ] Implement pre-commit hooks (lint-staged, Husky) for code quality

### 8. Performance & SEO Optimization 🚀
**Priority: MEDIUM - Important for user experience and discoverability**
- [ ] Replace all `<img>` tags with Next.js `Image` component
- [ ] Add Schema.org structured data for services and organization
- [ ] Implement OpenGraph tags and Twitter cards
- [ ] Add sitemap.xml and robots.txt
- [ ] Audit and lazy-load non-critical components
- [ ] Add skeleton loaders for slow-loading content

---

## **FUTURE CONSIDERATIONS**
> These items are planned for post-MVP implementation

### Accessibility Improvements
- [ ] Comprehensive WCAG audit
- [ ] Enhanced keyboard navigation
- [ ] Screen reader optimization
- [ ] Color contrast improvements

### Progressive Web App (PWA)
- [ ] Add PWA manifest and service worker
- [ ] Implement offline caching strategy
- [ ] Add install prompt for mobile users

### Advanced Features
- [ ] Error boundary components with user-friendly fallbacks
- [ ] Production monitoring (Sentry, LogRocket)
- [ ] Advanced analytics and user behavior tracking
- [ ] Cookie consent banner for GDPR compliance
- [ ] Rate limiting and spam protection for forms
- [ ] API request caching with SWR or React Query

### Development Experience
- [ ] Environment variable validation with Zod
- [ ] Visual regression testing (Chromatic, Percy)
- [ ] Automated dependency updates (Renovate, Dependabot)
- [ ] Comprehensive documentation (CONTRIBUTING.md, architecture.md)
- [ ] Enhanced CI/CD pipeline with deployment previews

---

## **ENVIRONMENT VARIABLES NEEDED**
```env
# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=existing

# Live Chat
NEXT_PUBLIC_CRISP_WEBSITE_ID=new
# OR
NEXT_PUBLIC_TAWK_TO_PROPERTY_ID=new

# Newsletter
NEXT_PUBLIC_MAILCHIMP_API_KEY=new
MAILCHIMP_LIST_ID=new
# OR
CONVERTKIT_API_KEY=new
CONVERTKIT_FORM_ID=new

# CMS
NEXT_PUBLIC_SANITY_PROJECT_ID=new
NEXT_PUBLIC_SANITY_DATASET=new
SANITY_API_TOKEN=new
```
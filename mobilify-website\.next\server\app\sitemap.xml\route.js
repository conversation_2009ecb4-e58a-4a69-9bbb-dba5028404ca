(()=>{var e={};e.id=475,e.ids=[475],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveManifest:function(){return a},resolveRobots:function(){return o},resolveRouteData:function(){return s},resolveSitemap:function(){return n}});let i=r(77341);function o(e){let t="";for(let r of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,i.resolveArray)(r.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(r.allow)for(let e of(0,i.resolveArray)(r.allow))t+=`Allow: ${e}
`;if(r.disallow)for(let e of(0,i.resolveArray)(r.disallow))t+=`Disallow: ${e}
`;r.crawlDelay&&(t+=`Crawl-delay: ${r.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,i.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function n(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),r=e.some(e=>{var t;return!!(null==(t=e.images)?void 0:t.length)}),i=e.some(e=>{var t;return!!(null==(t=e.videos)?void 0:t.length)}),o="";for(let l of(o+='<?xml version="1.0" encoding="UTF-8"?>\n',o+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',r&&(o+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),i&&(o+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),t?o+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':o+=">\n",e)){var n,a,s;o+="<url>\n",o+=`<loc>${l.url}</loc>
`;let e=null==(n=l.alternates)?void 0:n.languages;if(e&&Object.keys(e).length)for(let t in e)o+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(null==(a=l.images)?void 0:a.length)for(let e of l.images)o+=`<image:image>
<image:loc>${e}</image:loc>
</image:image>
`;if(null==(s=l.videos)?void 0:s.length)for(let e of l.videos)o+=["<video:video>",`<video:title>${e.title}</video:title>`,`<video:thumbnail_loc>${e.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${e.description}</video:description>`,e.content_loc&&`<video:content_loc>${e.content_loc}</video:content_loc>`,e.player_loc&&`<video:player_loc>${e.player_loc}</video:player_loc>`,e.duration&&`<video:duration>${e.duration}</video:duration>`,e.view_count&&`<video:view_count>${e.view_count}</video:view_count>`,e.tag&&`<video:tag>${e.tag}</video:tag>`,e.rating&&`<video:rating>${e.rating}</video:rating>`,e.expiration_date&&`<video:expiration_date>${e.expiration_date}</video:expiration_date>`,e.publication_date&&`<video:publication_date>${e.publication_date}</video:publication_date>`,e.family_friendly&&`<video:family_friendly>${e.family_friendly}</video:family_friendly>`,e.requires_subscription&&`<video:requires_subscription>${e.requires_subscription}</video:requires_subscription>`,e.live&&`<video:live>${e.live}</video:live>`,e.restriction&&`<video:restriction relationship="${e.restriction.relationship}">${e.restriction.content}</video:restriction>`,e.platform&&`<video:platform relationship="${e.platform.relationship}">${e.platform.content}</video:platform>`,e.uploader&&`<video:uploader${e.uploader.info&&` info="${e.uploader.info}"`}>${e.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(l.lastModified){let e=l.lastModified instanceof Date?l.lastModified.toISOString():l.lastModified;o+=`<lastmod>${e}</lastmod>
`}l.changeFrequency&&(o+=`<changefreq>${l.changeFrequency}</changefreq>
`),"number"==typeof l.priority&&(o+=`<priority>${l.priority}</priority>
`),o+="</url>\n"}return o+"</urlset>\n"}function a(e){return JSON.stringify(e)}function s(e,t){return"robots"===t?o(e):"sitemap"===t?n(e):"manifest"===t?a(e):""}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77341:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e:[e]}function i(e){if(null!=e)return r(e)}function o(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getOrigin:function(){return o},resolveArray:function(){return r},resolveAsArrayOrUndefined:function(){return i}})},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{default:()=>d});var o={};r.r(o),r.d(o,{GET:()=>f});var n=r(96559),a=r(48088),s=r(37719),l=r(32190),u=r(97569);async function d(){let e="https://mobilify.com",t=await (0,u.zX)();return[{url:e,lastModified:new Date,changeFrequency:"weekly",priority:1},{url:`${e}/about`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/services`,lastModified:new Date,changeFrequency:"monthly",priority:.9},{url:`${e}/blog`,lastModified:new Date,changeFrequency:"weekly",priority:.8},{url:`${e}/faq`,lastModified:new Date,changeFrequency:"monthly",priority:.7},...t.map(t=>({url:`${e}/blog/${t.slug.current}`,lastModified:new Date(t._updatedAt||t.publishedAt),changeFrequency:"monthly",priority:.6}))]}var c=r(12127);let p={...i}.default;if("function"!=typeof p)throw Error('Default export is missing in "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts"');async function f(e,t){let{__metadata_id__:r,...i}=await t.params||{},o=!!r&&r.endsWith(".xml");if(r&&!o)return new l.NextResponse("Not Found",{status:404});let n=r&&o?r.slice(0,-4):void 0,a=await p({id:n}),s=(0,c.resolveRouteData)(a,"sitemap");return new l.NextResponse(s,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let m=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Csuper%5COneDrive%5CDesktop%5CMy%20projects%5CMobilify%5Cwebsite%5Cgemini%5Cmobilify-website%5Csrc%5Capp%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:o}),{workAsyncStorage:v,workUnitAsyncStorage:g,serverHooks:y}=m;function _(){return(0,s.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:g})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97569:(e,t,r)=>{"use strict";r.d(t,{N7:()=>p,QU:()=>f,dk:()=>a,x8:()=>m,zX:()=>c});var i=r(25588);!function(){var e=Error("Cannot find module '@sanity/image-url'");throw e.code="MODULE_NOT_FOUND",e}();let o=(0,i.UU)({projectId:process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,dataset:process.env.NEXT_PUBLIC_SANITY_DATASET||"production",apiVersion:"2024-01-01",useCdn:!0,token:process.env.SANITY_API_TOKEN}),n=Object(function(){var e=Error("Cannot find module '@sanity/image-url'");throw e.code="MODULE_NOT_FOUND",e}())(o);function a(e){return n.image(e)}let s=`*[_type == "post" && defined(slug.current)] | order(publishedAt desc) {
  _id,
  title,
  slug,
  author,
  mainImage,
  categories[]-> {
    _id,
    title,
    slug
  },
  publishedAt,
  excerpt,
  _createdAt
}`,l=`*[_type == "post" && slug.current == $slug][0] {
  _id,
  title,
  slug,
  author,
  mainImage,
  categories[]-> {
    _id,
    title,
    slug,
    description
  },
  publishedAt,
  excerpt,
  body,
  _createdAt,
  _updatedAt
}`,u=`*[_type == "category"] | order(title asc) {
  _id,
  title,
  slug,
  description
}`,d=`*[_type == "post" && _id != $postId && count(categories[@._ref in $categoryIds]) > 0] | order(publishedAt desc)[0...3] {
  _id,
  title,
  slug,
  mainImage,
  publishedAt,
  excerpt
}`;async function c(){return await o.fetch(s)}async function p(e){return await o.fetch(l,{slug:e})}async function f(){return await o.fetch(u)}async function m(e,t){return await o.fetch(d,{postId:e,categoryIds:t})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,588,580],()=>r(83746));module.exports=i})();
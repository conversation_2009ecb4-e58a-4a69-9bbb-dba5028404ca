exports.id=225,exports.ids=[225],exports.modules={1188:(e,t,s)=>{"use strict";s.d(t,{DP:()=>n,ThemeProvider:()=>l});var o=s(60687),r=s(43210);let i=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},l=({children:e})=>{let[t,s]=(0,r.useState)("light"),[n,l]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s(e||t),l(!0)},[]),(0,r.useEffect)(()=>{if(!n)return;let e=document.documentElement;"dark"===t?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",t)},[t,n]),(0,r.useEffect)(()=>{if(!n)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||s(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[n]),n)?(0,o.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")},setTheme:e=>{s(e)}},children:e}):(0,o.jsx)("div",{className:"min-h-screen bg-white",children:e})}},8569:(e,t,s)=>{"use strict";s.d(t,{default:()=>r,l:()=>i});var o=s(43210);let r=({websiteId:e})=>((0,o.useEffect)(()=>e||process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID?()=>{}:void console.warn("Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable."),[e]),null),i={openChat:()=>{},closeChat:()=>{},showChat:()=>{},hideChat:()=>{},setUser:e=>{},sendMessage:e=>{},setSessionData:e=>{},isChatAvailable:()=>!1,setSegments:e=>{}}},14083:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var o=s(12907);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\CrispChat.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CrispChat.tsx","default");(0,o.registerClientReference)(function(){throw Error("Attempted to call crispUtils() from the server but crispUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CrispChat.tsx","crispUtils")},47641:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},47689:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var o=s(43210);let r=()=>((0,o.useEffect)(()=>{},[]),null)},61135:()=>{},68462:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>r});var o=s(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","useTheme");let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","ThemeProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useSystemTheme() from the server but useSystemTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","useSystemTheme"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getThemeClasses() from the server but getThemeClasses is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","getThemeClasses")},78438:(e,t,s)=>{Promise.resolve().then(s.bind(s,8569)),Promise.resolve().then(s.bind(s,47689)),Promise.resolve().then(s.bind(s,1188))},84089:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},86686:(e,t,s)=>{Promise.resolve().then(s.bind(s,14083)),Promise.resolve().then(s.bind(s,97023)),Promise.resolve().then(s.bind(s,68462))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>m});var o=s(37413),r=s(35759),i=s.n(r);s(61135);var n=s(97023),l=s(14083),a=s(68462);let m={title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"};function c({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${i().variable} font-sans antialiased text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-900 transition-colors duration-300`,children:(0,o.jsxs)(a.ThemeProvider,{children:[e,(0,o.jsx)(n.default,{}),(0,o.jsx)(l.default,{})]})})})}},97023:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});let o=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\GoogleAnalytics.tsx","default")}};
(()=>{var e={};e.id=784,e.ids=[784],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveManifest:function(){return a},resolveRobots:function(){return o},resolveRouteData:function(){return l},resolveSitemap:function(){return n}});let i=r(77341);function o(e){let t="";for(let r of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,i.resolveArray)(r.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(r.allow)for(let e of(0,i.resolveArray)(r.allow))t+=`Allow: ${e}
`;if(r.disallow)for(let e of(0,i.resolveArray)(r.disallow))t+=`Disallow: ${e}
`;r.crawlDelay&&(t+=`Crawl-delay: ${r.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,i.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function n(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),r=e.some(e=>{var t;return!!(null==(t=e.images)?void 0:t.length)}),i=e.some(e=>{var t;return!!(null==(t=e.videos)?void 0:t.length)}),o="";for(let s of(o+='<?xml version="1.0" encoding="UTF-8"?>\n',o+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',r&&(o+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),i&&(o+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),t?o+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':o+=">\n",e)){var n,a,l;o+="<url>\n",o+=`<loc>${s.url}</loc>
`;let e=null==(n=s.alternates)?void 0:n.languages;if(e&&Object.keys(e).length)for(let t in e)o+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(null==(a=s.images)?void 0:a.length)for(let e of s.images)o+=`<image:image>
<image:loc>${e}</image:loc>
</image:image>
`;if(null==(l=s.videos)?void 0:l.length)for(let e of s.videos)o+=["<video:video>",`<video:title>${e.title}</video:title>`,`<video:thumbnail_loc>${e.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${e.description}</video:description>`,e.content_loc&&`<video:content_loc>${e.content_loc}</video:content_loc>`,e.player_loc&&`<video:player_loc>${e.player_loc}</video:player_loc>`,e.duration&&`<video:duration>${e.duration}</video:duration>`,e.view_count&&`<video:view_count>${e.view_count}</video:view_count>`,e.tag&&`<video:tag>${e.tag}</video:tag>`,e.rating&&`<video:rating>${e.rating}</video:rating>`,e.expiration_date&&`<video:expiration_date>${e.expiration_date}</video:expiration_date>`,e.publication_date&&`<video:publication_date>${e.publication_date}</video:publication_date>`,e.family_friendly&&`<video:family_friendly>${e.family_friendly}</video:family_friendly>`,e.requires_subscription&&`<video:requires_subscription>${e.requires_subscription}</video:requires_subscription>`,e.live&&`<video:live>${e.live}</video:live>`,e.restriction&&`<video:restriction relationship="${e.restriction.relationship}">${e.restriction.content}</video:restriction>`,e.platform&&`<video:platform relationship="${e.platform.relationship}">${e.platform.content}</video:platform>`,e.uploader&&`<video:uploader${e.uploader.info&&` info="${e.uploader.info}"`}>${e.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(s.lastModified){let e=s.lastModified instanceof Date?s.lastModified.toISOString():s.lastModified;o+=`<lastmod>${e}</lastmod>
`}s.changeFrequency&&(o+=`<changefreq>${s.changeFrequency}</changefreq>
`),"number"==typeof s.priority&&(o+=`<priority>${s.priority}</priority>
`),o+="</url>\n"}return o+"</urlset>\n"}function a(e){return JSON.stringify(e)}function l(e,t){return"robots"===t?o(e):"sitemap"===t?n(e):"manifest"===t?a(e):""}},16398:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var i={};r.r(i),r.d(i,{GET:()=>u});var o=r(96559),n=r(48088),a=r(37719),l=r(32190),s=r(12127);async function u(){let e=await {rules:{userAgent:"*",allow:"/",disallow:["/api/","/admin/","/_next/","/studio/"]},sitemap:"https://mobilify.com/sitemap.xml"},t=(0,s.resolveRouteData)(e,"robots");return new l.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Csuper%5COneDrive%5CDesktop%5CMy%20projects%5CMobilify%5Cwebsite%5Cgemini%5Cmobilify-website%5Csrc%5Capp%5Crobots.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:i}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:v}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77341:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e:[e]}function i(e){if(null!=e)return r(e)}function o(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getOrigin:function(){return o},resolveArray:function(){return r},resolveAsArrayOrUndefined:function(){return i}})},78335:()=>{},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,580],()=>r(16398));module.exports=i})();
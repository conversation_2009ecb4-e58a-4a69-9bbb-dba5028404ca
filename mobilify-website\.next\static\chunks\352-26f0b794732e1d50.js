(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[352],{760:(t,e,r)=>{"use strict";r.d(e,{N:()=>v});var n=r(5155),i=r(2115),o=r(869),s=r(2885),a=r(7494),u=r(845),c=r(7351),l=r(1508);class h extends i.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,r=(0,c.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(t){let{children:e,isPresent:r,anchorX:o,root:s}=t,a=(0,i.useId)(),u=(0,i.useRef)(null),c=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=(0,i.useContext)(l.Q);return(0,i.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:i,right:l}=c.current;if(r||!u.current||!t||!e)return;u.current.dataset.motionPopId=a;let h=document.createElement("style");f&&(h.nonce=f);let p=null!=s?s:document.head;return p.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(l),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{p.removeChild(h),p.contains(h)&&p.removeChild(h)}},[r]),(0,n.jsx)(h,{isPresent:r,childRef:u,sizeRef:c,children:i.cloneElement(e,{ref:u})})}let p=t=>{let{children:e,initial:r,isPresent:o,onExitComplete:a,custom:c,presenceAffectsLayout:l,mode:h,anchorX:p,root:y}=t,g=(0,s.M)(d),m=(0,i.useId)(),v=!0,b=(0,i.useMemo)(()=>(v=!1,{id:m,initial:r,isPresent:o,custom:c,onExitComplete:t=>{for(let e of(g.set(t,!0),g.values()))if(!e)return;a&&a()},register:t=>(g.set(t,!1),()=>g.delete(t))}),[o,g,a]);return l&&v&&(b={...b}),(0,i.useMemo)(()=>{g.forEach((t,e)=>g.set(e,!1))},[o]),i.useEffect(()=>{o||g.size||!a||a()},[o]),"popLayout"===h&&(e=(0,n.jsx)(f,{isPresent:o,anchorX:p,root:y,children:e})),(0,n.jsx)(u.t.Provider,{value:b,children:e})};function d(){return new Map}var y=r(2082);let g=t=>t.key||"";function m(t){let e=[];return i.Children.forEach(t,t=>{(0,i.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:r,initial:u=!0,onExitComplete:c,presenceAffectsLayout:l=!0,mode:h="sync",propagate:f=!1,anchorX:d="left",root:v}=t,[b,w]=(0,y.xQ)(f),E=(0,i.useMemo)(()=>m(e),[e]),C=f&&!b?[]:E.map(g),x=(0,i.useRef)(!0),R=(0,i.useRef)(E),O=(0,s.M)(()=>new Map),[I,A]=(0,i.useState)(E),[q,S]=(0,i.useState)(E);(0,a.E)(()=>{x.current=!1,R.current=E;for(let t=0;t<q.length;t++){let e=g(q[t]);C.includes(e)?O.delete(e):!0!==O.get(e)&&O.set(e,!1)}},[q,C.length,C.join("-")]);let j=[];if(E!==I){let t=[...E];for(let e=0;e<q.length;e++){let r=q[e],n=g(r);C.includes(n)||(t.splice(e,0,r),j.push(r))}return"wait"===h&&j.length&&(t=j),S(m(t)),A(E),null}let{forceRender:_}=(0,i.useContext)(o.L);return(0,n.jsx)(n.Fragment,{children:q.map(t=>{let e=g(t),i=(!f||!!b)&&(E===q||C.includes(e));return(0,n.jsx)(p,{isPresent:i,initial:(!x.current||!!u)&&void 0,custom:r,presenceAffectsLayout:l,mode:h,root:v,onExitComplete:i?void 0:()=>{if(!O.has(e))return;O.set(e,!0);let t=!0;O.forEach(e=>{e||(t=!1)}),t&&(null==_||_(),S(R.current),f&&(null==w||w()),c&&c())},anchorX:d,children:t},e)})})}},1469:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{default:function(){return u},getImageProps:function(){return a}});let n=r(8229),i=r(8883),o=r(3063),s=n._(r(1193));function a(t){let{props:e}=(0,i.getImgProps)(t,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,r]of Object.entries(e))void 0===r&&delete e[t];return{props:e}}let u=o.Image},1501:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}r.d(e,{C:()=>a,Q:()=>l,u4:()=>n});var i={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},o={0:8203,1:8204,2:8205,3:65279},s=[,,,,].fill(String.fromCodePoint(o[0])).join("");function a(t,e,r="auto"){let n;return!0===r||"auto"===r&&(!(!Number.isNaN(Number(t))||/[a-z]/i.test(t)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(t))&&Date.parse(t)||function(t){try{new URL(t,t.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(t))?t:`${t}${n=JSON.stringify(e),`${s}${Array.from(n).map(t=>{let e=t.charCodeAt(0);if(e>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${n} on character ${t} (${e})`);return Array.from(e.toString(4).padStart(4,"0")).map(t=>String.fromCodePoint(o[t])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(o).map(t=>t.reverse())),Object.fromEntries(Object.entries(i).map(t=>t.reverse()));var u=`${Object.values(i).map(t=>`\\u{${t.toString(16)}}`).join("")}`,c=RegExp(`[${u}]{4,}`,"gu");function l(t){var e,r;return t&&JSON.parse({cleaned:(e=JSON.stringify(t)).replace(c,""),encoded:(null==(r=e.match(c))?void 0:r[0])||""}.cleaned)}},3406:(t,e,r)=>{"use strict";r.d(e,{UU:()=>nw});let n=!(typeof navigator>"u")&&"ReactNative"===navigator.product,i={timeout:n?6e4:12e4},o=function(t){let e={...i,..."string"==typeof t?{url:t}:t};if(e.timeout=function t(e){if(!1===e||0===e)return!1;if(e.connect||e.socket)return e;let r=Number(e);return isNaN(r)?t(i.timeout):{connect:r,socket:r}}(e.timeout),e.query){let{url:t,searchParams:r}=function(t){let e=t.indexOf("?");if(-1===e)return{url:t,searchParams:new URLSearchParams};let r=t.slice(0,e),i=t.slice(e+1);if(!n)return{url:r,searchParams:new URLSearchParams(i)};if("function"!=typeof decodeURIComponent)throw Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");let o=new URLSearchParams;for(let t of i.split("&")){let[e,r]=t.split("=");e&&o.append(s(e),s(r||""))}return{url:r,searchParams:o}}(e.url);for(let[n,i]of Object.entries(e.query)){if(void 0!==i)if(Array.isArray(i))for(let t of i)r.append(n,t);else r.append(n,i);let o=r.toString();o&&(e.url=`${t}?${o}`)}}return e.method=e.body&&!e.method?"POST":(e.method||"GET").toUpperCase(),e};function s(t){return decodeURIComponent(t.replace(/\+/g," "))}let a=/^https?:\/\//i,u=function(t){if(!a.test(t.url))throw Error(`"${t.url}" is not a valid URL`)};function c(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}let l=["request","response","progress","error","abort"],h=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];var f,p,d=c(function(){if(p)return f;p=1;var t=function(t){return t.replace(/^\s+|\s+$/g,"")};return f=function(e){if(!e)return{};for(var r=Object.create(null),n=t(e).split("\n"),i=0;i<n.length;i++){var o,s=n[i],a=s.indexOf(":"),u=t(s.slice(0,a)).toLowerCase(),c=t(s.slice(a+1));typeof r[u]>"u"?r[u]=c:(o=r[u],"[object Array]"===Object.prototype.toString.call(o))?r[u].push(c):r[u]=[r[u],c]}return r}}());class y{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#t;#e;#r;#n={};#i;#o={};#s;open(t,e,r){this.#t=t,this.#e=e,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#i=void 0}abort(){this.#i&&this.#i.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(t,e){this.#n[t]=e}setInit(t,e=!0){this.#o=t,this.#s=e}send(t){let e="arraybuffer"!==this.responseType,r={...this.#o,method:this.#t,headers:this.#n,body:t};"function"==typeof AbortController&&this.#s&&(this.#i=new AbortController,"u">typeof EventTarget&&this.#i.signal instanceof EventTarget&&(r.signal=this.#i.signal)),"u">typeof document&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#e,r).then(t=>(t.headers.forEach((t,e)=>{this.#r+=`${e}: ${t}\r
`}),this.status=t.status,this.statusText=t.statusText,this.readyState=3,this.onreadystatechange?.(),e?t.text():t.arrayBuffer())).then(t=>{"string"==typeof t?this.responseText=t:this.response=t,this.readyState=4,this.onreadystatechange?.()}).catch(t=>{"AbortError"!==t.name?this.onerror?.(t):this.onabort?.()})}}let g="function"==typeof XMLHttpRequest?"xhr":"fetch",m="xhr"===g?XMLHttpRequest:y,v=(t,e)=>{let r=t.options,n=t.applyMiddleware("finalizeOptions",r),i={},o=t.applyMiddleware("interceptRequest",void 0,{adapter:g,context:t});if(o){let t=setTimeout(e,0,null,o);return{abort:()=>clearTimeout(t)}}let s=new m;s instanceof y&&"object"==typeof n.fetch&&s.setInit(n.fetch,n.useAbortSignal??!0);let a=n.headers,u=n.timeout,c=!1,l=!1,h=!1;if(s.onerror=t=>{v(s instanceof y?t instanceof Error?t:Error(`Request error while attempting to reach is ${n.url}`,{cause:t}):Error(`Request error while attempting to reach is ${n.url}${t.lengthComputable?`(${t.loaded} of ${t.total} bytes transferred)`:""}`))},s.ontimeout=t=>{v(Error(`Request timeout while attempting to reach ${n.url}${t.lengthComputable?`(${t.loaded} of ${t.total} bytes transferred)`:""}`))},s.onabort=()=>{p(!0),c=!0},s.onreadystatechange=function(){u&&(p(),i.socket=setTimeout(()=>f("ESOCKETTIMEDOUT"),u.socket)),!c&&s&&4===s.readyState&&0!==s.status&&function(){if(!(c||l||h)){if(0===s.status)return v(Error("Unknown XHR error"));p(),l=!0,e(null,{body:s.response||(""===s.responseType||"text"===s.responseType?s.responseText:""),url:n.url,method:n.method,headers:d(s.getAllResponseHeaders()),statusCode:s.status,statusMessage:s.statusText})}}()},s.open(n.method,n.url,!0),s.withCredentials=!!n.withCredentials,a&&s.setRequestHeader)for(let t in a)a.hasOwnProperty(t)&&s.setRequestHeader(t,a[t]);return n.rawBody&&(s.responseType="arraybuffer"),t.applyMiddleware("onRequest",{options:n,adapter:g,request:s,context:t}),s.send(n.body||null),u&&(i.connect=setTimeout(()=>f("ETIMEDOUT"),u.connect)),{abort:function(){c=!0,s&&s.abort()}};function f(e){h=!0,s.abort();let r=Error("ESOCKETTIMEDOUT"===e?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=e,t.channels.error.publish(r)}function p(t){(t||c||s&&s.readyState>=2&&i.connect)&&clearTimeout(i.connect),i.socket&&clearTimeout(i.socket)}function v(t){if(l)return;p(!0),l=!0,s=null;let r=t||Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,e(r)}},b=(t=[],e=v)=>(function t(e,r){let n=[],i=h.reduce((t,e)=>(t[e]=t[e]||[],t),{processOptions:[o],validateOptions:[u]});function s(t){let e,n=l.reduce((t,e)=>(t[e]=function(){let t=Object.create(null),e=0;return{publish:function(e){for(let r in t)t[r](e)},subscribe:function(r){let n=e++;return t[n]=r,function(){delete t[n]}}}}(),t),{}),o=function(t,e,...r){let n="onError"===t,o=e;for(let e=0;e<i[t].length&&(o=(0,i[t][e])(o,...r),!n||o);e++);return o},s=o("processOptions",t);o("validateOptions",s);let a={options:s,channels:n,applyMiddleware:o},u=n.request.subscribe(t=>{e=r(t,(e,r)=>((t,e,r)=>{let i=t,s=e;if(!i)try{s=o("onResponse",e,r)}catch(t){s=null,i=t}(i=i&&o("onError",i,r))?n.error.publish(i):s&&n.response.publish(s)})(e,r,t))});n.abort.subscribe(()=>{u(),e&&e.abort()});let c=o("onReturn",n,a);return c===n&&n.request.publish(a),c}return s.use=function(t){if(!t)throw Error("Tried to add middleware that resolved to falsey value");if("function"==typeof t)throw Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(t.onReturn&&i.onReturn.length>0)throw Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return h.forEach(e=>{t[e]&&i[e].push(t[e])}),n.push(t),s},s.clone=()=>t(n,r),e.forEach(s.use),s})(t,e);var w=r(9509),E=r(9641).Buffer,C,x,R,O,I,A={exports:{}};I||(I=1,function(t,e){let r;e.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,t=>{"%%"!==t&&(n++,"%c"===t&&(i=n))}),e.splice(i,0,r)},e.save=function(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch{}},e.load=function(){let t;try{t=e.storage.getItem("debug")||e.storage.getItem("DEBUG")}catch{}return!t&&"u">typeof w&&"env"in w&&(t=w.env.DEBUG),t},e.useColors=function(){let t;return"u">typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("u">typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"u">typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"u">typeof navigator&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},e.storage=function(){try{return localStorage}catch{}}(),r=!1,e.destroy=()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))},e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=(O?R:(O=1,R=function(t){function e(t){let n,i,o,s=null;function a(...t){if(!a.enabled)return;let r=Number(new Date);a.diff=r-(n||r),a.prev=n,a.curr=r,n=r,t[0]=e.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");let i=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let o=e.formatters[n];if("function"==typeof o){let e=t[i];r=o.call(a,e),t.splice(i,1),i--}return r}),e.formatArgs.call(a,t),(a.log||e.log).apply(a,t)}return a.namespace=t,a.useColors=e.useColors(),a.color=e.selectColor(t),a.extend=r,a.destroy=e.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==e.namespaces&&(i=e.namespaces,o=e.enabled(t)),o),set:t=>{s=t}}),"function"==typeof e.init&&e.init(a),a}function r(t,r){let n=e(this.namespace+(typeof r>"u"?":":r)+t);return n.log=this.log,n}function n(t,e){let r=0,n=0,i=-1,o=0;for(;r<t.length;)if(n<e.length&&(e[n]===t[r]||"*"===e[n]))"*"===e[n]?(i=n,o=r):r++,n++;else{if(-1===i)return!1;n=i+1,r=++o}for(;n<e.length&&"*"===e[n];)n++;return n===e.length}return e.debug=e,e.default=e,e.coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){let t=[...e.names,...e.skips.map(t=>"-"+t)].join(",");return e.enable(""),t},e.enable=function(t){for(let r of(e.save(t),e.namespaces=t,e.names=[],e.skips=[],("string"==typeof t?t:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===r[0]?e.skips.push(r.slice(1)):e.names.push(r)},e.enabled=function(t){for(let r of e.skips)if(n(t,r))return!1;for(let r of e.names)if(n(t,r))return!0;return!1},e.humanize=function(){if(x)return C;function t(t,e,r,n){return Math.round(t/r)+" "+n+(e>=1.5*r?"s":"")}return x=1,C=function(e,r){r=r||{};var n,i,o=typeof e;if("string"===o&&e.length>0){var s=e;if(!((s=String(s)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(s);if(a){var u=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*u;case"weeks":case"week":case"w":return 6048e5*u;case"days":case"day":case"d":return 864e5*u;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*u;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*u;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u}}}return}if("number"===o&&isFinite(e))return r.long?(i=Math.abs(e))>=864e5?t(e,i,864e5,"day"):i>=36e5?t(e,i,36e5,"hour"):i>=6e4?t(e,i,6e4,"minute"):i>=1e3?t(e,i,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}}(),e.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(t).forEach(r=>{e[r]=t[r]}),e.names=[],e.skips=[],e.formatters={},e.selectColor=function(t){let r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e)|0;return e.colors[Math.abs(r)%e.colors.length]},e.enable(e.load()),e}))(e);let{formatters:n}=t.exports;n.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}}(A,A.exports)),A.exports,Object.prototype.hasOwnProperty;let q=typeof E>"u"?()=>!1:t=>E.isBuffer(t);function S(t){return"[object Object]"===Object.prototype.toString.call(t)}let j=["boolean","string","number"],_={};"u">typeof globalThis?_=globalThis:"u">typeof window?_=window:"u">typeof global?_=global:"u">typeof self&&(_=self);var T=_;let $=(t={})=>{let e=t.implementation||Promise;if(!e)throw Error("`Promise` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new e((e,i)=>{let o=n.options.cancelToken;o&&o.promise.then(t=>{r.abort.publish(t),i(t)}),r.error.subscribe(i),r.response.subscribe(r=>{e(t.onlyBody?r.body:r)}),setTimeout(()=>{try{r.request.publish(n)}catch(t){i(t)}},0)})}};class P{__CANCEL__=!0;message;constructor(t){this.message=t}toString(){return"Cancel"+(this.message?`: ${this.message}`:"")}}class M{promise;reason;constructor(t){if("function"!=typeof t)throw TypeError("executor must be a function.");let e=null;this.promise=new Promise(t=>{e=t}),t(t=>{this.reason||(this.reason=new P(t),e(this.reason))})}static source=()=>{let t;return{token:new M(e=>{t=e}),cancel:t}}}$.Cancel=P,$.CancelToken=M,$.isCancel=t=>!(!t||!t?.__CANCEL__);var U=(t,e,r)=>("GET"===r.method||"HEAD"===r.method)&&(t.isNetworkError||!1);function k(t){return 100*Math.pow(2,t)+100*Math.random()}let B=(t={})=>(t=>{let e=t.maxRetries||5,r=t.retryDelay||k,n=t.shouldRetry;return{onError:(t,i)=>{var o;let s=i.options,a=s.maxRetries||e,u=s.retryDelay||r,c=s.shouldRetry||n,l=s.attemptNumber||0;if(null!==(o=s.body)&&"object"==typeof o&&"function"==typeof o.pipe||!c(t,l,s)||l>=a)return t;let h=Object.assign({},i,{options:Object.assign({},s,{attemptNumber:l+1})});return setTimeout(()=>i.channels.request.publish(h),u(l)),null}}})({shouldRetry:U,...t});B.shouldRetry=U;var F=function(t,e){return(F=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function D(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}F(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function L(t,e){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(u){var c=[a,u];if(r)throw TypeError("Generator is already executing.");for(;s&&(s=0,c[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&c[0]?n.return:c[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,c[1])).done)return i;switch(n=0,i&&(c=[2&c[0],i.value]),c[0]){case 0:case 1:i=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,n=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(i=(i=o.trys).length>0&&i[i.length-1])&&(6===c[0]||2===c[0])){o=0;continue}if(3===c[0]&&(!i||c[1]>i[0]&&c[1]<i[3])){o.label=c[1];break}if(6===c[0]&&o.label<i[1]){o.label=i[1],i=c;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(c);break}i[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(t,o)}catch(t){c=[6,t],n=0}finally{r=i=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}}function N(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function z(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s}function V(t,e,r){if(r||2==arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}function H(t){return this instanceof H?(this.v=t,this):new H(t)}function W(t){return"function"==typeof t}function J(t){var e=t(function(t){Error.call(t),t.stack=Error().stack});return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var Y=J(function(t){return function(e){t(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map(function(t,e){return e+1+") "+t.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e}});function G(t,e){if(t){var r=t.indexOf(e);0<=r&&t.splice(r,1)}}var Q=function(){var t;function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var t,e,r,n,i,o=this._parentage;if(o)if(this._parentage=null,Array.isArray(o))try{for(var s=N(o),a=s.next();!a.done;a=s.next())a.value.remove(this)}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}else o.remove(this);var u=this.initialTeardown;if(W(u))try{u()}catch(t){i=t instanceof Y?t.errors:[t]}var c=this._finalizers;if(c){this._finalizers=null;try{for(var l=N(c),h=l.next();!h.done;h=l.next()){var f=h.value;try{K(f)}catch(t){i=null!=i?i:[],t instanceof Y?i=V(V([],z(i)),z(t.errors)):i.push(t)}}}catch(t){r={error:t}}finally{try{h&&!h.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}}if(i)throw new Y(i)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)K(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!=(r=this._finalizers)?r:[]).push(t)}},e.prototype._hasParent=function(t){var e=this._parentage;return e===t||Array.isArray(e)&&e.includes(t)},e.prototype._addParent=function(t){var e=this._parentage;this._parentage=Array.isArray(e)?(e.push(t),e):e?[e,t]:t},e.prototype._removeParent=function(t){var e=this._parentage;e===t?this._parentage=null:Array.isArray(e)&&G(e,t)},e.prototype.remove=function(t){var r=this._finalizers;r&&G(r,t),t instanceof e&&t._removeParent(this)},(t=new e).closed=!0,e.EMPTY=t,e}(),X=Q.EMPTY;function Z(t){return t instanceof Q||t&&"closed"in t&&W(t.remove)&&W(t.add)&&W(t.unsubscribe)}function K(t){W(t)?t():t.unsubscribe()}var tt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},te={setTimeout:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=te.delegate;return(null==i?void 0:i.setTimeout)?i.setTimeout.apply(i,V([t,e],z(r))):setTimeout.apply(void 0,V([t,e],z(r)))},clearTimeout:function(t){var e=te.delegate;return((null==e?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function tr(t){te.setTimeout(function(){var e=tt.onUnhandledError;if(e)e(t);else throw t})}function tn(){}var ti=to("C",void 0,void 0);function to(t,e,r){return{kind:t,value:e,error:r}}var ts=null;function ta(t){if(tt.useDeprecatedSynchronousErrorHandling){var e=!ts;if(e&&(ts={errorThrown:!1,error:null}),t(),e){var r=ts,n=r.errorThrown,i=r.error;if(ts=null,n)throw i}}else t()}var tu=function(t){function e(e){var r=t.call(this)||this;return r.isStopped=!1,e?(r.destination=e,Z(e)&&e.add(r)):r.destination=ty,r}return D(e,t),e.create=function(t,e,r){return new tf(t,e,r)},e.prototype.next=function(t){this.isStopped?td(to("N",t,void 0),this):this._next(t)},e.prototype.error=function(t){this.isStopped?td(to("E",void 0,t),this):(this.isStopped=!0,this._error(t))},e.prototype.complete=function(){this.isStopped?td(ti,this):(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(t){this.destination.next(t)},e.prototype._error=function(t){try{this.destination.error(t)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(Q),tc=Function.prototype.bind;function tl(t,e){return tc.call(t,e)}var th=function(){function t(t){this.partialObserver=t}return t.prototype.next=function(t){var e=this.partialObserver;if(e.next)try{e.next(t)}catch(t){tp(t)}},t.prototype.error=function(t){var e=this.partialObserver;if(e.error)try{e.error(t)}catch(t){tp(t)}else tp(t)},t.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(t){tp(t)}},t}(),tf=function(t){function e(e,r,n){var i,o,s=t.call(this)||this;return W(e)||!e?i={next:null!=e?e:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:s&&tt.useDeprecatedNextContext?((o=Object.create(e)).unsubscribe=function(){return s.unsubscribe()},i={next:e.next&&tl(e.next,o),error:e.error&&tl(e.error,o),complete:e.complete&&tl(e.complete,o)}):i=e,s.destination=new th(i),s}return D(e,t),e}(tu);function tp(t){if(tt.useDeprecatedSynchronousErrorHandling)tt.useDeprecatedSynchronousErrorHandling&&ts&&(ts.errorThrown=!0,ts.error=t);else tr(t)}function td(t,e){var r=tt.onStoppedNotification;r&&te.setTimeout(function(){return r(t,e)})}var ty={closed:!0,next:tn,error:function(t){throw t},complete:tn},tg="function"==typeof Symbol&&Symbol.observable||"@@observable";function tm(t){return t}function tv(t){return 0===t.length?tm:1===t.length?t[0]:function(e){return t.reduce(function(t,e){return e(t)},e)}}var tb=function(){function t(t){t&&(this._subscribe=t)}return t.prototype.lift=function(e){var r=new t;return r.source=this,r.operator=e,r},t.prototype.subscribe=function(t,e,r){var n=this,i=!function(t){return t&&t instanceof tu||t&&W(t.next)&&W(t.error)&&W(t.complete)&&Z(t)}(t)?new tf(t,e,r):t;return ta(function(){var t=n.operator,e=n.source;i.add(t?t.call(i,e):e?n._subscribe(i):n._trySubscribe(i))}),i},t.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(e){t.error(e)}},t.prototype.forEach=function(t,e){var r=this;return new(e=tw(e))(function(e,n){var i=new tf({next:function(e){try{t(e)}catch(t){n(t),i.unsubscribe()}},error:n,complete:e});r.subscribe(i)})},t.prototype._subscribe=function(t){var e;return null==(e=this.source)?void 0:e.subscribe(t)},t.prototype[tg]=function(){return this},t.prototype.pipe=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return tv(t)(this)},t.prototype.toPromise=function(t){var e=this;return new(t=tw(t))(function(t,r){var n;e.subscribe(function(t){return n=t},function(t){return r(t)},function(){return t(n)})})},t.create=function(e){return new t(e)},t}();function tw(t){var e;return null!=(e=null!=t?t:tt.Promise)?e:Promise}var tE=function(t){return t&&"number"==typeof t.length&&"function"!=typeof t};function tC(t){return W(null==t?void 0:t.then)}function tx(t){return Symbol.asyncIterator&&W(null==t?void 0:t[Symbol.asyncIterator])}function tR(t){return TypeError("You provided "+(null!==t&&"object"==typeof t?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var tO="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function tI(t){return W(null==t?void 0:t[tO])}function tA(t){return function(t,e,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(t,e||[]),o=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),s("next"),s("throw"),s("return",function(t){return function(e){return Promise.resolve(e).then(t,c)}}),n[Symbol.asyncIterator]=function(){return this},n;function s(t,e){i[t]&&(n[t]=function(e){return new Promise(function(r,n){o.push([t,e,r,n])>1||a(t,e)})},e&&(n[t]=e(n[t])))}function a(t,e){try{var r;(r=i[t](e)).value instanceof H?Promise.resolve(r.value.v).then(u,c):l(o[0][2],r)}catch(t){l(o[0][3],t)}}function u(t){a("next",t)}function c(t){a("throw",t)}function l(t,e){t(e),o.shift(),o.length&&a(o[0][0],o[0][1])}}(this,arguments,function(){var e,r,n;return L(this,function(i){switch(i.label){case 0:e=t.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,H(e.read())];case 3:if(n=(r=i.sent()).value,!r.done)return[3,5];return[4,H(void 0)];case 4:return[2,i.sent()];case 5:return[4,H(n)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return e.releaseLock(),[7];case 10:return[2]}})})}function tq(t){return W(null==t?void 0:t.getReader)}function tS(t){if(t instanceof tb)return t;if(null!=t){var e,r,n,i;if(W(t[tg])){return e=t,new tb(function(t){var r=e[tg]();if(W(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")})}if(tE(t)){return r=t,new tb(function(t){for(var e=0;e<r.length&&!t.closed;e++)t.next(r[e]);t.complete()})}if(tC(t)){return n=t,new tb(function(t){n.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,tr)})}if(tx(t))return tj(t);if(tI(t)){return i=t,new tb(function(t){var e,r;try{for(var n=N(i),o=n.next();!o.done;o=n.next()){var s=o.value;if(t.next(s),t.closed)return}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}t.complete()})}if(tq(t))return tj(tA(t))}throw tR(t)}function tj(t){return new tb(function(e){(function(t,e){var r,n,i,o,s,a,u,c;return s=this,a=void 0,u=void 0,c=function(){var s;return L(this,function(a){switch(a.label){case 0:a.trys.push([0,5,6,11]),r=function(t){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t=N(t),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(r){e[r]=t[r]&&function(e){return new Promise(function(n,i){var o,s,a;o=n,s=i,a=(e=t[r](e)).done,Promise.resolve(e.value).then(function(t){o({value:t,done:a})},s)})}}}(t),a.label=1;case 1:return[4,r.next()];case 2:if((n=a.sent()).done)return[3,4];if(s=n.value,e.next(s),e.closed)return[2];a.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return i={error:a.sent()},[3,11];case 6:if(a.trys.push([6,,9,10]),!(n&&!n.done&&(o=r.return)))return[3,8];return[4,o.call(r)];case 7:a.sent(),a.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}})},new(u||(u=Promise))(function(t,e){function r(t){try{i(c.next(t))}catch(t){e(t)}}function n(t){try{i(c.throw(t))}catch(t){e(t)}}function i(e){var i;e.done?t(e.value):((i=e.value)instanceof u?i:new u(function(t){t(i)})).then(r,n)}i((c=c.apply(s,a||[])).next())})})(t,e).catch(function(t){return e.error(t)})})}function t_(t){return new tb(function(e){tS(t()).subscribe(e)})}function tT(t){return t[t.length-1]}function t$(t){var e;return(e=tT(t))&&W(e.schedule)?t.pop():void 0}function tP(t,e,r,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var o=e.schedule(function(){r(),i?t.add(this.schedule(null,n)):this.unsubscribe()},n);if(t.add(o),!i)return o}function tM(t){return function(e){if(W(null==e?void 0:e.lift))return e.lift(function(e){try{return t(e,this)}catch(t){this.error(t)}});throw TypeError("Unable to lift unknown Observable type")}}function tU(t,e,r,n,i){return new tk(t,e,r,n,i)}var tk=function(t){function e(e,r,n,i,o,s){var a=t.call(this,e)||this;return a.onFinalize=o,a.shouldUnsubscribe=s,a._next=r?function(t){try{r(t)}catch(t){e.error(t)}}:t.prototype._next,a._error=i?function(t){try{i(t)}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._error,a._complete=n?function(){try{n()}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._complete,a}return D(e,t),e.prototype.unsubscribe=function(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;t.prototype.unsubscribe.call(this),r||null==(e=this.onFinalize)||e.call(this)}},e}(tu);function tB(t,e){return void 0===e&&(e=0),tM(function(r,n){r.subscribe(tU(n,function(r){return tP(n,t,function(){return n.next(r)},e)},function(){return tP(n,t,function(){return n.complete()},e)},function(r){return tP(n,t,function(){return n.error(r)},e)}))})}function tF(t,e){return void 0===e&&(e=0),tM(function(r,n){n.add(t.schedule(function(){return r.subscribe(n)},e))})}function tD(t,e){if(!t)throw Error("Iterable cannot be null");return new tb(function(r){tP(r,e,function(){var n=t[Symbol.asyncIterator]();tP(r,e,function(){n.next().then(function(t){t.done?r.complete():r.next(t.value)})},0,!0)})})}function tL(t,e){return e?function(t,e){if(null!=t){if(W(t[tg]))return tS(t).pipe(tF(e),tB(e));if(tE(t))return new tb(function(r){var n=0;return e.schedule(function(){n===t.length?r.complete():(r.next(t[n++]),r.closed||this.schedule())})});if(tC(t))return tS(t).pipe(tF(e),tB(e));if(tx(t))return tD(t,e);if(tI(t))return new tb(function(r){var n;return tP(r,e,function(){n=t[tO](),tP(r,e,function(){var t,e,i;try{e=(t=n.next()).value,i=t.done}catch(t){r.error(t);return}i?r.complete():r.next(e)},0,!0)}),function(){return W(null==n?void 0:n.return)&&n.return()}});if(tq(t))return tD(tA(t),e)}throw tR(t)}(t,e):tS(t)}function tN(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=t$(t);return tL(t,r)}function tz(t,e){return tM(function(r,n){var i=0;r.subscribe(tU(n,function(r){n.next(t.call(e,r,i++))}))})}function tV(t,e,r){return(void 0===r&&(r=1/0),W(e))?tV(function(r,n){return tz(function(t,i){return e(r,t,n,i)})(tS(t(r,n)))},r):("number"==typeof e&&(r=e),tM(function(e,n){var i,o,s,a,u,c,l,h,f;return i=r,s=[],a=0,u=0,c=!1,l=function(){!c||s.length||a||n.complete()},h=function(t){return a<i?f(t):s.push(t)},f=function(e){a++;var r=!1;tS(t(e,u++)).subscribe(tU(n,function(t){o?h(t):n.next(t)},function(){r=!0},void 0,function(){if(r)try{for(a--;s.length&&a<i;)!function(){var t=s.shift();f(t)}();l()}catch(t){n.error(t)}}))},e.subscribe(tU(n,h,function(){c=!0,l()})),function(){}}))}var tH=J(function(t){return function(){t(this),this.name="EmptyError",this.message="no elements in sequence"}});function tW(t,e){var r="object"==typeof e;return new Promise(function(n,i){var o,s=!1;t.subscribe({next:function(t){o=t,s=!0},error:i,complete:function(){s?n(o):r?n(e.defaultValue):i(new tH)}})})}var tJ=J(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),tY=function(t){function e(){var e=t.call(this)||this;return e.closed=!1,e.currentObservers=null,e.observers=[],e.isStopped=!1,e.hasError=!1,e.thrownError=null,e}return D(e,t),e.prototype.lift=function(t){var e=new tG(this,this);return e.operator=t,e},e.prototype._throwIfClosed=function(){if(this.closed)throw new tJ},e.prototype.next=function(t){var e=this;ta(function(){var r,n;if(e._throwIfClosed(),!e.isStopped){e.currentObservers||(e.currentObservers=Array.from(e.observers));try{for(var i=N(e.currentObservers),o=i.next();!o.done;o=i.next())o.value.next(t)}catch(t){r={error:t}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}}})},e.prototype.error=function(t){var e=this;ta(function(){if(e._throwIfClosed(),!e.isStopped){e.hasError=e.isStopped=!0,e.thrownError=t;for(var r=e.observers;r.length;)r.shift().error(t)}})},e.prototype.complete=function(){var t=this;ta(function(){if(t._throwIfClosed(),!t.isStopped){t.isStopped=!0;for(var e=t.observers;e.length;)e.shift().complete()}})},e.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(e.prototype,"observed",{get:function(){var t;return(null==(t=this.observers)?void 0:t.length)>0},enumerable:!1,configurable:!0}),e.prototype._trySubscribe=function(e){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,e)},e.prototype._subscribe=function(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)},e.prototype._innerSubscribe=function(t){var e=this,r=this.hasError,n=this.isStopped,i=this.observers;return r||n?X:(this.currentObservers=null,i.push(t),new Q(function(){e.currentObservers=null,G(i,t)}))},e.prototype._checkFinalizedStatuses=function(t){var e=this.hasError,r=this.thrownError,n=this.isStopped;e?t.error(r):n&&t.complete()},e.prototype.asObservable=function(){var t=new tb;return t.source=this,t},e.create=function(t,e){return new tG(t,e)},e}(tb),tG=function(t){function e(e,r){var n=t.call(this)||this;return n.destination=e,n.source=r,n}return D(e,t),e.prototype.next=function(t){var e,r;null==(r=null==(e=this.destination)?void 0:e.next)||r.call(e,t)},e.prototype.error=function(t){var e,r;null==(r=null==(e=this.destination)?void 0:e.error)||r.call(e,t)},e.prototype.complete=function(){var t,e;null==(e=null==(t=this.destination)?void 0:t.complete)||e.call(t)},e.prototype._subscribe=function(t){var e,r;return null!=(r=null==(e=this.source)?void 0:e.subscribe(t))?r:X},e}(tY),tQ={now:function(){return(tQ.delegate||Date).now()},delegate:void 0},tX=function(t){function e(e,r,n){void 0===e&&(e=1/0),void 0===r&&(r=1/0),void 0===n&&(n=tQ);var i=t.call(this)||this;return i._bufferSize=e,i._windowTime=r,i._timestampProvider=n,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=r===1/0,i._bufferSize=Math.max(1,e),i._windowTime=Math.max(1,r),i}return D(e,t),e.prototype.next=function(e){var r=this.isStopped,n=this._buffer,i=this._infiniteTimeWindow,o=this._timestampProvider,s=this._windowTime;!r&&(n.push(e),i||n.push(o.now()+s)),this._trimBuffer(),t.prototype.next.call(this,e)},e.prototype._subscribe=function(t){this._throwIfClosed(),this._trimBuffer();for(var e=this._innerSubscribe(t),r=this._infiniteTimeWindow,n=this._buffer.slice(),i=0;i<n.length&&!t.closed;i+=r?1:2)t.next(n[i]);return this._checkFinalizedStatuses(t),e},e.prototype._trimBuffer=function(){var t=this._bufferSize,e=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,i=(n?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!n){for(var o=e.now(),s=0,a=1;a<r.length&&r[a]<=o;a+=2)s=a;s&&r.splice(0,s+1)}},e}(tY);function tZ(t){void 0===t&&(t={});var e=t.connector,r=void 0===e?function(){return new tY}:e,n=t.resetOnError,i=void 0===n||n,o=t.resetOnComplete,s=void 0===o||o,a=t.resetOnRefCountZero,u=void 0===a||a;return function(t){var e,n,o,a=0,c=!1,l=!1,h=function(){null==n||n.unsubscribe(),n=void 0},f=function(){h(),e=o=void 0,c=l=!1},p=function(){var t=e;f(),null==t||t.unsubscribe()};return tM(function(t,d){a++,l||c||h();var y=o=null!=o?o:r();d.add(function(){0!=--a||l||c||(n=tK(p,u))}),y.subscribe(d),!e&&a>0&&(e=new tf({next:function(t){return y.next(t)},error:function(t){l=!0,h(),n=tK(f,i,t),y.error(t)},complete:function(){c=!0,h(),n=tK(f,s),y.complete()}}),tS(t).subscribe(e))})(t)}}function tK(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(!0===e)return void t();if(!1!==e){var i=new tf({next:function(){i.unsubscribe(),t()}});return tS(e.apply(void 0,V([],z(r)))).subscribe(i)}}function t0(t){return tM(function(e,r){var n,i=null,o=!1;i=e.subscribe(tU(r,void 0,void 0,function(s){n=tS(t(s,t0(t)(e))),i?(i.unsubscribe(),i=null,n.subscribe(r)):o=!0})),o&&(i.unsubscribe(),i=null,n.subscribe(r))})}function t1(t){return void 0===t&&(t=1/0),tV(tm,t)}function t2(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t1(1)(tL(t,t$(t)))}var t3=function(t){function e(e,r){return t.call(this)||this}return D(e,t),e.prototype.schedule=function(t,e){return void 0===e&&(e=0),this},e}(Q),t6={setInterval:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=t6.delegate;return(null==i?void 0:i.setInterval)?i.setInterval.apply(i,V([t,e],z(r))):setInterval.apply(void 0,V([t,e],z(r)))},clearInterval:function(t){var e=t6.delegate;return((null==e?void 0:e.clearInterval)||clearInterval)(t)},delegate:void 0},t5=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.scheduler=e,n.work=r,n.pending=!1,n}return D(e,t),e.prototype.schedule=function(t,e){if(void 0===e&&(e=0),this.closed)return this;this.state=t;var r,n=this.id,i=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(i,n,e)),this.pending=!0,this.delay=e,this.id=null!=(r=this.id)?r:this.requestAsyncId(i,this.id,e),this},e.prototype.requestAsyncId=function(t,e,r){return void 0===r&&(r=0),t6.setInterval(t.flush.bind(t,this),r)},e.prototype.recycleAsyncId=function(t,e,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return e;null!=e&&t6.clearInterval(e)},e.prototype.execute=function(t,e){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(t,e);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(t,e){var r,n=!1;try{this.work(t)}catch(t){n=!0,r=t||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},e.prototype.unsubscribe=function(){if(!this.closed){var e=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,G(n,this),null!=e&&(this.id=this.recycleAsyncId(r,e,null)),this.delay=null,t.prototype.unsubscribe.call(this)}},e}(t3),t8=function(){function t(e,r){void 0===r&&(r=t.now),this.schedulerActionCtor=e,this.now=r}return t.prototype.schedule=function(t,e,r){return void 0===e&&(e=0),new this.schedulerActionCtor(this,t).schedule(r,e)},t.now=tQ.now,t}(),t4=new(function(t){function e(e,r){void 0===r&&(r=t8.now);var n=t.call(this,e,r)||this;return n.actions=[],n._active=!1,n}return D(e,t),e.prototype.flush=function(t){var e,r=this.actions;if(this._active)return void r.push(t);this._active=!0;do if(e=t.execute(t.state,t.delay))break;while(t=r.shift());if(this._active=!1,e){for(;t=r.shift();)t.unsubscribe();throw e}},e}(t8))(t5);function t9(t,e){var r=W(t)?t:function(){return t},n=function(t){return t.error(r())};return new tb(e?function(t){return e.schedule(n,0,t)}:n)}function t7(t){return tM(function(e,r){try{e.subscribe(r)}finally{r.add(t)}})}var et=new tb(function(t){return t.complete()});function ee(t,e){var r="object"==typeof e;return new Promise(function(n,i){var o=new tf({next:function(t){n(t),o.unsubscribe()},error:i,complete:function(){r?n(e.defaultValue):i(new tH)}});t.subscribe(o)})}var er=r(1501),en=Array.isArray,ei=Array.isArray;function eo(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=W(tT(t))?t.pop():void 0;return r?function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return tv(t)}(eo.apply(void 0,V([],z(t))),tz(function(t){return ei(t)?r.apply(void 0,V([],z(t))):r(t)})):tM(function(e,r){var n,i,o;(n=V([e],z(1===t.length&&en(t[0])?t[0]:t)),void 0===o&&(o=tm),function(t){var e,r,s;e=void 0,r=function(){for(var e=n.length,r=Array(e),s=e,a=e,u=function(e){var u,c,l;u=i,c=function(){var u=tL(n[e],i),c=!1;u.subscribe(tU(t,function(n){r[e]=n,!c&&(c=!0,a--),a||t.next(o(r.slice()))},function(){--s||t.complete()}))},l=t,u?tP(l,u,c):c()},c=0;c<e;c++)u(c)},s=t,e?tP(s,e,r):r()})(r)})}function es(t,e){return tM(function(r,n){var i=0;r.subscribe(tU(n,function(r){return t.call(e,r,i++)&&n.next(r)}))})}let ea=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,eu=/_key\s*==\s*['"](.*)['"]/,ec=/^\d*:\d*$/;function el(t){return"string"==typeof t?eu.test(t.trim()):"object"==typeof t&&"_key"in t}function eh(t){var e;return"number"==typeof(e=t)||"string"==typeof e&&/^\[\d+\]$/.test(e)?Number(t.replace(/[^\d]/g,"")):el(t)?{_key:t.match(eu)[1]}:!function(t){if("string"==typeof t&&ec.test(t))return!0;if(!Array.isArray(t)||2!==t.length)return!1;let[e,r]=t;return("number"==typeof e||""===e)&&("number"==typeof r||""===r)}(t)?t:function(t){let[e,r]=t.split(":").map(t=>""===t?t:Number(t));return[e,r]}(t)}let ef="drafts.",ep="versions.";function ed(t){return t.startsWith(ef)}function ey(t){return t.startsWith(ep)}function eg(t,e){if("drafts"===e||"published"===e)throw Error('Version can not be "published" or "drafts"');return`${ep}${e}.${ev(t)}`}function em(t){if(!ey(t))return;let[e,r,...n]=t.split(".");return r}function ev(t){return ey(t)?t.split(".").slice(2).join("."):ed(t)?t.slice(ef.length):t}let eb=t=>crypto.getRandomValues(new Uint8Array(t)),ew=(t,e,r)=>{let n=(2<<Math.log(t.length-1)/Math.LN2)-1,i=-~(1.6*n*e/t.length);return (o=e)=>{let s="";for(;;){let e=r(i),a=0|i;for(;a--;)if((s+=t[e[a]&n]||"").length===o)return s}}},eE=/\r\n|[\n\r\u2028\u2029]/;function eC(t,e){let r=0;for(let n=0;n<e.length;n++){let i=e[n].length+1;if(r+i>t)return{line:n+1,column:t-r};r+=i}return{line:e.length,column:e[e.length-1]?.length??0}}class ex extends Error{response;statusCode=400;responseBody;details;constructor(t,e){let r=eO(t,e);super(r.message),Object.assign(this,r)}}class eR extends Error{response;statusCode=500;responseBody;details;constructor(t){let e=eO(t);super(e.message),Object.assign(this,e)}}function eO(t,e){var r,n,i;let o=t.body,s={response:t,statusCode:t.statusCode,responseBody:(r=o,-1!==(t.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(r,null,2):r),message:"",details:void 0};if(!(0,er.u4)(o))return s.message=eq(t,o),s;let a=o.error;if("string"==typeof a&&"string"==typeof o.message)return s.message=`${a} - ${o.message}`,s;if("object"!=typeof a||null===a)return"string"==typeof a?s.message=a:"string"==typeof o.message?s.message=o.message:s.message=eq(t,o),s;if("type"in(n=a)&&"mutationError"===n.type&&"description"in n&&"string"==typeof n.description||"type"in(i=a)&&"actionError"===i.type&&"description"in i&&"string"==typeof i.description){let t=a.items||[],e=t.slice(0,5).map(t=>t.error?.description).filter(Boolean),r=e.length?`:
- ${e.join(`
- `)}`:"";return t.length>5&&(r+=`
...and ${t.length-5} more`),s.message=`${a.description}${r}`,s.details=o.error,s}return eI(a)?(s.message=eA(a,e?.options?.query?.tag),s.details=o.error):"description"in a&&"string"==typeof a.description?(s.message=a.description,s.details=a):s.message=eq(t,o),s}function eI(t){return(0,er.u4)(t)&&"queryParseError"===t.type&&"string"==typeof t.query&&"number"==typeof t.start&&"number"==typeof t.end}function eA(t,e){let{query:r,start:n,end:i,description:o}=t;if(!r||typeof n>"u")return`GROQ query parse error: ${o}`;let s=e?`

Tag: ${e}`:"";return`GROQ query parse error:
${function(t,e,r){let n=t.split(eE),{start:i,end:o,markerLines:s}=function(t,e){let r={...t.start},n={...r,...t.end},i=r.line??-1,o=r.column??0,s=n.line,a=n.column,u=Math.max(i-3,0),c=Math.min(e.length,s+3);-1===i&&(u=0),-1===s&&(c=e.length);let l=s-i,h={};if(l)for(let t=0;t<=l;t++){let r=t+i;if(o)if(0===t){let t=e[r-1].length;h[r]=[o,t-o+1]}else if(t===l)h[r]=[0,a];else{let n=e[r-t].length;h[r]=[0,n]}else h[r]=!0}else o===a?o?h[i]=[o,0]:h[i]=!0:h[i]=[o,a-o];return{start:u,end:c,markerLines:h}}({start:eC(e.start,n),end:e.end?eC(e.end,n):void 0},n),a=`${o}`.length;return t.split(eE,o).slice(i,o).map((t,e)=>{let n=i+1+e,o=` ${` ${n}`.slice(-a)} |`,u=s[n],c=!s[n+1];if(!u)return` ${o}${t.length>0?` ${t}`:""}`;let l="";if(Array.isArray(u)){let e=t.slice(0,Math.max(u[0]-1,0)).replace(/[^\t]/g," "),n=u[1]||1;l=[`
 `,o.replace(/\d/g," ")," ",e,"^".repeat(n)].join(""),c&&r&&(l+=" "+r)}return[">",o,t.length>0?` ${t}`:"",l].join("")}).join(`
`)}(r,{start:n,end:i},o)}${s}`}function eq(t,e){var r,n;let i="string"==typeof e?` (${n=100,(r=e).length>100?`${r.slice(0,n)}\u2026`:r})`:"",o=t.statusMessage?` ${t.statusMessage}`:"";return`${t.method}-request to ${t.url} resulted in HTTP ${t.statusCode}${o}${i}`}class eS extends Error{projectId;addOriginUrl;constructor({projectId:t}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=t;let e=new URL(`https://sanity.io/manage/project/${t}/api`);if("u">typeof location){let{origin:t}=location;e.searchParams.set("cors","add"),e.searchParams.set("origin",t),this.addOriginUrl=e,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${e}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${e}`}}let ej={onResponse:(t,e)=>{if(t.statusCode>=500)throw new eR(t);if(t.statusCode>=400)throw new ex(t,e);return t}};function e_(t){return b([B({shouldRetry:eT}),...t,function(){let t={};return{onResponse:e=>{let r=e.headers["x-sanity-warning"];for(let e of Array.isArray(r)?r:[r])!e||t[e]||(t[e]=!0,console.warn(e));return e}}}(),{processOptions:t=>{let e=t.body;return!e||"function"==typeof e.pipe||q(e)||-1===j.indexOf(typeof e)&&!Array.isArray(e)&&!function(t){if(!1===S(t))return!1;let e=t.constructor;if(void 0===e)return!0;let r=e.prototype;return!1!==S(r)&&!1!==r.hasOwnProperty("isPrototypeOf")}(e)?t:Object.assign({},t,{body:JSON.stringify(t.body),headers:Object.assign({},t.headers,{"Content-Type":"application/json"})})}},{onResponse:t=>{let e=t.headers["content-type"]||"",r=-1!==e.indexOf("application/json");return t.body&&e&&r?Object.assign({},t,{body:function(t){try{return JSON.parse(t)}catch(t){throw t.message=`Failed to parsed response body as JSON: ${t.message}`,t}}(t.body)}):t},processOptions:t=>Object.assign({},t,{headers:Object.assign({Accept:"application/json"},t.headers)})},{onRequest:t=>{if("xhr"!==t.adapter)return;let e=t.request,r=t.context;function n(t){return e=>{let n=e.lengthComputable?e.loaded/e.total*100:-1;r.channels.progress.publish({stage:t,percent:n,total:e.total,loaded:e.loaded,lengthComputable:e.lengthComputable})}}"upload"in e&&"onprogress"in e.upload&&(e.upload.onprogress=n("upload")),"onprogress"in e&&(e.onprogress=n("download"))}},ej,function(t={}){let e=t.implementation||T.Observable;if(!e)throw Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(t,r)=>new e(e=>(t.error.subscribe(t=>e.error(t)),t.progress.subscribe(t=>e.next(Object.assign({type:"progress"},t))),t.response.subscribe(t=>{e.next(Object.assign({type:"response"},t)),e.complete()}),t.request.publish(r),()=>t.abort.publish()))}}({implementation:tb})])}function eT(t,e,r){if(0===r.maxRetries)return!1;let n="GET"===r.method||"HEAD"===r.method,i=(r.uri||r.url).startsWith("/data/query"),o=t.response&&(429===t.response.statusCode||502===t.response.statusCode||503===t.response.statusCode);return(!!n||!!i)&&!!o||B.shouldRetry(t,e,r)}function e$(t){return"https://www.sanity.io/help/"+t}let eP=["image","file"],eM=["before","after","replace"],eU=t=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(t))throw Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},ek=t=>{if(!/^[-a-z0-9]+$/i.test(t))throw Error("`projectId` can only contain only a-z, 0-9 and dashes")},eB=t=>{if(-1===eP.indexOf(t))throw Error(`Invalid asset type: ${t}. Must be one of ${eP.join(", ")}`)},eF=(t,e)=>{if(null===e||"object"!=typeof e||Array.isArray(e))throw Error(`${t}() takes an object of properties`)},eD=(t,e)=>{if("string"!=typeof e||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(e)||e.includes(".."))throw Error(`${t}(): "${e}" is not a valid document ID`)},eL=(t,e)=>{if(!e._id)throw Error(`${t}() requires that the document contains an ID ("_id" property)`);eD(t,e._id)},eN=(t,e)=>{if("string"!=typeof e)throw Error(`\`${t}()\`: \`${e}\` is not a valid document type`)},ez=(t,e)=>{if(!e._type)throw Error(`\`${t}()\` requires that the document contains a type (\`_type\` property)`);eN(t,e._type)},eV=(t,e)=>{if(e._id&&e._id!==t)throw Error(`The provided document ID (\`${e._id}\`) does not match the generated version ID (\`${t}\`)`)},eH=(t,e,r)=>{let n="insert(at, selector, items)";if(-1===eM.indexOf(t)){let t=eM.map(t=>`"${t}"`).join(", ");throw Error(`${n} takes an "at"-argument which is one of: ${t}`)}if("string"!=typeof e)throw Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw Error(`${n} takes an "items"-argument which must be an array`)},eW=t=>{if(!t.dataset)throw Error("`dataset` must be provided to perform queries");return t.dataset||""},eJ=t=>{if("string"!=typeof t||!/^[a-z0-9._-]{1,75}$/i.test(t))throw Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return t},eY=t=>{if(!t["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":if(2!==r.split(".").length)throw Error('Dataset resource ID must be in the format "project.dataset"');return;case"dashboard":case"media-library":case"canvas":return;default:throw Error(`Unsupported resource type: ${e.toString()}`)}},eG=(t,e)=>{if(e["~experimental_resource"])throw Error(`\`${t}\` does not support resource-based operations`)},eQ=t=>(function(t){let e=!1,r;return(...n)=>(e||(r=t(...n),e=!0),r)})((...e)=>console.warn(t.join(" "),...e)),eX=eQ(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),eZ=eQ(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),eK=eQ(["The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),e0=eQ(["The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version"]),e1=eQ(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${e$("js-client-browser-token")} for more information and how to hide this warning.`]),e2=eQ(["You have configured Sanity client to use a token, but also provided `withCredentials: true`.","This is no longer supported - only token will be used - remove `withCredentials: true`."]),e3=eQ(["Using the Sanity client without specifying an API version is deprecated.",`See ${e$("js-client-api-version")}`]),e6=(eQ(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),{apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}}),e5=["localhost","127.0.0.1","0.0.0.0"],e8=t=>-1!==e5.indexOf(t);function e4(t){if(Array.isArray(t)&&t.length>1&&t.includes("raw"))throw TypeError('Invalid API perspective value: "raw". The raw-perspective can not be combined with other perspectives')}let e9=(t,e)=>{let r={...e,...t,stega:{..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||e6.stega,..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||{}}};r.apiVersion||e3();let n={...e6,...r},i=n.useProjectHostname&&!n["~experimental_resource"];if(typeof Promise>"u"){let t=e$("js-client-promise-polyfill");throw Error(`No native Promise-implementation found, polyfill needed - see ${t}`)}if(i&&!n.projectId)throw Error("Configuration must contain `projectId`");if(n["~experimental_resource"]&&eY(n),"u">typeof n.perspective&&e4(n.perspective),"encodeSourceMap"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof n.stega.enabled)throw Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&void 0===n.stega.studioUrl)throw Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&"string"!=typeof n.stega.studioUrl&&"function"!=typeof n.stega.studioUrl)throw Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);let o="u">typeof window&&window.location&&window.location.hostname,s=o&&e8(window.location.hostname),a=!!n.token;n.withCredentials&&a&&(e2(),n.withCredentials=!1),o&&s&&a&&!0!==n.ignoreBrowserTokenWarning?e1():typeof n.useCdn>"u"&&eZ(),i&&ek(n.projectId),n.dataset&&eU(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?eJ(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===e6.apiHost,!0===n.useCdn&&n.withCredentials&&eX(),n.useCdn=!1!==n.useCdn&&!n.withCredentials,function(t){if("1"===t||"X"===t)return;let e=new Date(t);if(!(/^\d{4}-\d{2}-\d{2}$/.test(t)&&e instanceof Date&&e.getTime()>0))throw Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(n.apiVersion);let u=n.apiHost.split("://",2),c=u[0],l=u[1],h=n.isDefaultApi?"apicdn.sanity.io":l;return i?(n.url=`${c}://${n.projectId}.${l}/v${n.apiVersion}`,n.cdnUrl=`${c}://${n.projectId}.${h}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};class e7 extends Error{name="ConnectionFailedError"}class rt extends Error{name="DisconnectError";reason;constructor(t,e,r={}){super(t,r),this.reason=e}}class re extends Error{name="ChannelError";data;constructor(t,e){super(t),this.data=e}}class rr extends Error{name="MessageError";data;constructor(t,e,r={}){super(t,r),this.data=e}}class rn extends Error{name="MessageParseError"}let ri=["channelError","disconnect"];function ro(t,e){return t_(()=>{let e=t();return e&&(e instanceof tb||W(e.lift)&&W(e.subscribe))?e:tN(e)}).pipe(tV(t=>{var r,n;return r=t,n=e,new tb(t=>{let e=n.includes("open"),i=n.includes("reconnect");function o(e){if("data"in e){let[r,n]=rs(e);t.error(r?new rn("Unable to parse EventSource error message",{cause:n}):new rr((n?.data).message,n));return}r.readyState===r.CLOSED?t.error(new e7("EventSource connection failed")):i&&t.next({type:"reconnect"})}function s(){t.next({type:"open"})}function a(e){let[n,i]=rs(e);if(n)return void t.error(new rn("Unable to parse EventSource message",{cause:n}));if("channelError"===e.type){let e=new URL(r.url).searchParams.get("tag");t.error(new re(function(t,e){let r=t.error;return r?eI(r)?eA(r,e):r.description?r.description:"string"==typeof r?r:JSON.stringify(r,null,2):t.message||"Unknown listener error"}(i?.data,e),i.data));return}if("disconnect"===e.type)return void t.error(new rt(`Server disconnected client: ${i.data?.reason||"unknown error"}`));t.next({type:e.type,id:e.lastEventId,...i.data?{data:i.data}:{}})}r.addEventListener("error",o),e&&r.addEventListener("open",s);let u=[...new Set([...ri,...n])].filter(t=>"error"!==t&&"open"!==t&&"reconnect"!==t);return u.forEach(t=>r.addEventListener(t,a)),()=>{r.removeEventListener("error",o),e&&r.removeEventListener("open",s),u.forEach(t=>r.removeEventListener(t,a)),r.close()}})}))}function rs(t){try{let e="string"==typeof t.data&&JSON.parse(t.data);return[null,{type:t.type,id:t.lastEventId,...!function(t){for(let e in t)return!1;return!0}(e)?{data:e}:{}}]}catch(t){return[t,null]}}function ra(t){if("string"==typeof t)return{id:t};if(Array.isArray(t))return{query:"*[_id in $ids]",params:{ids:t}};if("object"==typeof t&&null!==t&&"query"in t&&"string"==typeof t.query)return"params"in t&&"object"==typeof t.params&&null!==t.params?{query:t.query,params:t.params}:{query:t.query};let e=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw Error(`Unknown selection - must be one of:

${e}`)}class ru{selection;operations;constructor(t,e={}){this.selection=t,this.operations=e}set(t){return this._assign("set",t)}setIfMissing(t){return this._assign("setIfMissing",t)}diffMatchPatch(t){return eF("diffMatchPatch",t),this._assign("diffMatchPatch",t)}unset(t){if(!Array.isArray(t))throw Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:t}),this}inc(t){return this._assign("inc",t)}dec(t){return this._assign("dec",t)}insert(t,e,r){return eH(t,e,r),this._assign("insert",{[t]:e,items:r})}append(t,e){return this.insert("after",`${t}[-1]`,e)}prepend(t,e){return this.insert("before",`${t}[0]`,e)}splice(t,e,r,n){let i=e<0?e-1:e,o=typeof r>"u"||-1===r?-1:Math.max(0,e+r),s=`${t}[${i}:${i<0&&o>=0?"":o}]`;return this.insert("replace",s,n||[])}ifRevisionId(t){return this.operations.ifRevisionID=t,this}serialize(){return{...ra(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(t,e,r=!0){return eF(t,e),this.operations=Object.assign({},this.operations,{[t]:Object.assign({},r&&this.operations[t]||{},e)}),this}_set(t,e){return this._assign(t,e,!1)}}class rc extends ru{#a;constructor(t,e,r){super(t,e),this.#a=r}clone(){return new rc(this.selection,{...this.operations},this.#a)}commit(t){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let e=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},t);return this.#a.mutate({patch:this.serialize()},e)}}class rl extends ru{#a;constructor(t,e,r){super(t,e),this.#a=r}clone(){return new rl(this.selection,{...this.operations},this.#a)}commit(t){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let e=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},t);return this.#a.mutate({patch:this.serialize()},e)}}let rh={returnDocuments:!1};class rf{operations;trxId;constructor(t=[],e){this.operations=t,this.trxId=e}create(t){return eF("create",t),this._add({create:t})}createIfNotExists(t){let e="createIfNotExists";return eF(e,t),eL(e,t),this._add({[e]:t})}createOrReplace(t){let e="createOrReplace";return eF(e,t),eL(e,t),this._add({[e]:t})}delete(t){return eD("delete",t),this._add({delete:{id:t}})}transactionId(t){return t?(this.trxId=t,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(t){return this.operations.push(t),this}}class rp extends rf{#a;constructor(t,e,r){super(t,r),this.#a=e}clone(){return new rp([...this.operations],this.#a,this.trxId)}commit(t){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},rh,t||{}))}patch(t,e){let r="function"==typeof e,n="string"!=typeof t&&t instanceof rl,i="object"==typeof t&&("query"in t||"id"in t);if(n)return this._add({patch:t.serialize()});if(r){let r=e(new rl(t,{},this.#a));if(!(r instanceof rl))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}if(i){let r=new rl(t,e||{},this.#a);return this._add({patch:r.serialize()})}return this._add({patch:{id:t,...e}})}}class rd extends rf{#a;constructor(t,e,r){super(t,r),this.#a=e}clone(){return new rd([...this.operations],this.#a,this.trxId)}commit(t){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},rh,t||{}))}patch(t,e){let r="function"==typeof e;if("string"!=typeof t&&t instanceof rc)return this._add({patch:t.serialize()});if(r){let r=e(new rc(t,{},this.#a));if(!(r instanceof rc))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:t,...e}})}}let ry=({query:t,params:e={},options:r={}})=>{let n=new URLSearchParams,{tag:i,includeMutations:o,returnQuery:s,...a}=r;for(let[r,o]of(i&&n.append("tag",i),n.append("query",t),Object.entries(e)))void 0!==o&&n.append(`$${r}`,JSON.stringify(o));for(let[t,e]of Object.entries(a))e&&n.append(t,`${e}`);return!1===s&&n.append("returnQuery","false"),!1===o&&n.append("includeMutations","false"),`?${n}`},rg=(t,e)=>!1===t?void 0:typeof t>"u"?e:t,rm=(t={})=>({dryRun:t.dryRun,returnIds:!0,returnDocuments:rg(t.returnDocuments,!0),visibility:t.visibility||"sync",autoGenerateArrayKeys:t.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:t.skipCrossDatasetReferenceValidation}),rv=t=>"response"===t.type,rb=t=>t.body,rw=(t,e)=>t.reduce((t,r)=>(t[e(r)]=r,t),Object.create(null));function rE(t,e,n,i,o={},s={}){let a="stega"in s?{...n||{},..."boolean"==typeof s.stega?{enabled:s.stega}:s.stega||{}}:n,u=a.enabled?(0,er.Q)(o):o,c=!1===s.filterResponse?t=>t:t=>t.result,{cache:l,next:h,...f}={useAbortSignal:"u">typeof s.signal,resultSourceMap:a.enabled?"withKeyArraySelector":s.resultSourceMap,...s,returnQuery:!1===s.filterResponse&&!1!==s.returnQuery},p=rP(t,e,"query",{query:i,params:u},"u">typeof l||"u">typeof h?{...f,fetch:{cache:l,next:h}}:f);return a.enabled?p.pipe(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return eo.apply(void 0,V([],z(t)))}(tL(r.e(195).then(r.bind(r,4195)).then(function(t){return t.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:t})=>t))),tz(([t,e])=>{let r=e(t.result,t.resultSourceMap,a);return c({...t,result:r})})):p.pipe(tz(c))}function rC(t,e,r,n={}){let i={uri:rH(t,"doc",(()=>{if(!n.releaseId)return r;let t=em(r);if(!t){if(ed(r))throw Error(`The document ID (\`${r}\`) is a draft, but \`options.releaseId\` is set as \`${n.releaseId}\``);return eg(r,n.releaseId)}if(t!==n.releaseId)throw Error(`The document ID (\`${r}\`) is already a version of \`${t}\` release, but this does not match the provided \`options.releaseId\` (\`${n.releaseId}\`)`);return r})()),json:!0,tag:n.tag,signal:n.signal};return rz(t,e,i).pipe(es(rv),tz(t=>t.body.documents&&t.body.documents[0]))}function rx(t,e,r,n={}){let i={uri:rH(t,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return rz(t,e,i).pipe(es(rv),tz(t=>{let e=rw(t.body.documents||[],t=>t._id);return r.map(t=>e[t]||null)}))}function rR(t,e,r,n={}){return rP(t,e,"query",{query:"*[sanity::partOfRelease($releaseId)]",params:{releaseId:r}},n)}function rO(t,e,r,n){return eL("createIfNotExists",r),rM(t,e,r,"createIfNotExists",n)}function rI(t,e,r,n){return eL("createOrReplace",r),rM(t,e,r,"createOrReplace",n)}function rA(t,e,r,n,i){return eL("createVersion",r),ez("createVersion",r),r$(t,e,{actionType:"sanity.action.document.version.create",publishedId:n,document:r},i)}function rq(t,e,r,n){return rP(t,e,"mutate",{mutations:[{delete:ra(r)}]},n)}function rS(t,e,r,n=!1,i){return r$(t,e,{actionType:"sanity.action.document.version.discard",versionId:r,purge:n},i)}function rj(t,e,r,n){return eL("replaceVersion",r),ez("replaceVersion",r),r$(t,e,{actionType:"sanity.action.document.version.replace",document:r},n)}function r_(t,e,r,n,i){return r$(t,e,{actionType:"sanity.action.document.version.unpublish",versionId:r,publishedId:n},i)}function rT(t,e,r,n){let i;return rP(t,e,"mutate",{mutations:Array.isArray(i=r instanceof rl||r instanceof rc?{patch:r.serialize()}:r instanceof rp||r instanceof rd?r.serialize():r)?i:[i],transactionId:n&&n.transactionId||void 0},n)}function r$(t,e,r,n){let i=Array.isArray(r)?r:[r],o=n&&n.transactionId||void 0;return rP(t,e,"actions",{actions:i,transactionId:o,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function rP(t,e,r,n,i={}){let o="mutate"===r,s="actions"===r,a=o||s?"":ry(n),u=!o&&!s&&a.length<11264,c=u?a:"",l=i.returnFirst,{timeout:h,token:f,tag:p,headers:d,returnQuery:y,lastLiveEventId:g,cacheMode:m}=i,v={method:u?"GET":"POST",uri:rH(t,r,c),json:!0,body:u?void 0:n,query:o&&rm(i),timeout:h,headers:d,token:f,tag:p,returnQuery:y,perspective:i.perspective,resultSourceMap:i.resultSourceMap,lastLiveEventId:Array.isArray(g)?g[0]:g,cacheMode:m,canUseCdn:"query"===r,signal:i.signal,fetch:i.fetch,useAbortSignal:i.useAbortSignal,useCdn:i.useCdn};return rz(t,e,v).pipe(es(rv),tz(rb),tz(t=>{if(!o)return t;let e=t.results||[];if(i.returnDocuments)return l?e[0]&&e[0].document:e.map(t=>t.document);let r=l?e[0]&&e[0].id:e.map(t=>t.id);return{transactionId:t.transactionId,results:e,[l?"documentId":"documentIds"]:r}}))}function rM(t,e,r,n,i={}){return rP(t,e,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},i))}let rU=t=>void 0!==t.config().dataset&&void 0!==t.config().projectId||void 0!==t.config()["~experimental_resource"],rk=(t,e)=>rU(t)&&e.startsWith(rH(t,"query")),rB=(t,e)=>rU(t)&&e.startsWith(rH(t,"mutate")),rF=(t,e)=>rU(t)&&e.startsWith(rH(t,"doc","")),rD=(t,e)=>rU(t)&&e.startsWith(rH(t,"listen")),rL=(t,e)=>rU(t)&&e.startsWith(rH(t,"history","")),rN=(t,e)=>e.startsWith("/data/")||rk(t,e)||rB(t,e)||rF(t,e)||rD(t,e)||rL(t,e);function rz(t,e,r){var n;let i=r.url||r.uri,o=t.config(),s=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&rN(t,i):r.canUseCdn,a=(r.useCdn??o.useCdn)&&s,u=r.tag&&o.requestTagPrefix?[o.requestTagPrefix,r.tag].join("."):r.tag||o.requestTagPrefix;if(u&&null!==r.tag&&(r.query={tag:eJ(u),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&rk(t,i)){let t=r.resultSourceMap??o.resultSourceMap;void 0!==t&&!1!==t&&(r.query={resultSourceMap:t,...r.query});let e=r.perspective||o.perspective;"u">typeof e&&("previewDrafts"===e&&e0(),e4(e),r.query={perspective:Array.isArray(e)?e.join(","):e,...r.query},(Array.isArray(e)&&e.length>0||"previewDrafts"===e||"drafts"===e)&&a&&(a=!1,eK())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),a&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}let c=function(t,e={}){let r={};t.headers&&Object.assign(r,t.headers);let n=e.token||t.token;n&&(r.Authorization=`Bearer ${n}`),e.useGlobalApi||t.useProjectHostname||!t.projectId||(r["X-Sanity-Project-ID"]=t.projectId);let i=!!(typeof e.withCredentials>"u"?t.withCredentials:e.withCredentials),o=typeof e.timeout>"u"?t.timeout:e.timeout;return Object.assign({},e,{headers:Object.assign({},r,e.headers||{}),timeout:typeof o>"u"?3e5:o,proxy:e.proxy||t.proxy,json:!0,withCredentials:i,fetch:"object"==typeof e.fetch&&"object"==typeof t.fetch?{...t.fetch,...e.fetch}:e.fetch||t.fetch})}(o,Object.assign({},r,{url:rW(t,i,a)})),l=new tb(t=>e(c,o.requester).subscribe(t));return r.signal?l.pipe((n=r.signal,t=>new tb(e=>{let r=()=>e.error(function(t){if(rJ)return new DOMException(t?.reason??"The operation was aborted.","AbortError");let e=Error(t?.reason??"The operation was aborted.");return e.name="AbortError",e}(n));if(n&&n.aborted)return void r();let i=t.subscribe(e);return n.addEventListener("abort",r),()=>{n.removeEventListener("abort",r),i.unsubscribe()}}))):l}function rV(t,e,r){return rz(t,e,r).pipe(es(t=>"response"===t.type),tz(t=>t.body))}function rH(t,e,r){let n=t.config();if(n["~experimental_resource"]){eY(n);let t=rY(n),i=void 0!==r?`${e}/${r}`:e;return`${t}/${i}`.replace(/\/($|\?)/,"$1")}let i=eW(n),o=`/${e}/${i}`;return`/data${void 0!==r?`${o}/${r}`:o}`.replace(/\/($|\?)/,"$1")}function rW(t,e,r=!1){let{url:n,cdnUrl:i}=t.config();return`${r?i:n}/${e.replace(/^\//,"")}`}let rJ=!!globalThis.DOMException,rY=t=>{if(!t["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":{let t=r.split(".");if(2!==t.length)throw Error('Dataset ID must be in the format "project.dataset"');return`/projects/${t[0]}/datasets/${t[1]}`}case"canvas":return`/canvases/${r}`;case"media-library":return`/media-libraries/${r}`;case"dashboard":return`/dashboards/${r}`;default:throw Error(`Unsupported resource type: ${e.toString()}`)}};function rG(t,e,r){let n=eW(t.config());return rV(t,e,{method:"POST",uri:`/agent/action/generate/${n}`,body:r})}function rQ(t,e,r){let n=eW(t.config());return rV(t,e,{method:"POST",uri:`/agent/action/transform/${n}`,body:r})}function rX(t,e,r){let n=eW(t.config());return rV(t,e,{method:"POST",uri:`/agent/action/translate/${n}`,body:r})}class rZ{#a;#u;constructor(t,e){this.#a=t,this.#u=e}generate(t){return rG(this.#a,this.#u,t)}transform(t){return rQ(this.#a,this.#u,t)}translate(t){return rX(this.#a,this.#u,t)}}class rK{#a;#u;constructor(t,e){this.#a=t,this.#u=e}generate(t){return tW(rG(this.#a,this.#u,t))}transform(t){return tW(rQ(this.#a,this.#u,t))}translate(t){return tW(rX(this.#a,this.#u,t))}prompt(t){return tW(function(t,e,r){let n=eW(t.config());return rV(t,e,{method:"POST",uri:`/agent/action/prompt/${n}`,body:r})}(this.#a,this.#u,t))}patch(t){return tW(function(t,e,r){let n=eW(t.config());return rV(t,e,{method:"POST",uri:`/agent/action/patch/${n}`,body:r})}(this.#a,this.#u,t))}}class r0{#a;#u;constructor(t,e){this.#a=t,this.#u=e}upload(t,e,r){return r2(this.#a,this.#u,t,e,r)}}class r1{#a;#u;constructor(t,e){this.#a=t,this.#u=e}upload(t,e,r){return tW(r2(this.#a,this.#u,t,e,r).pipe(es(t=>"response"===t.type),tz(t=>t.body.document)))}}function r2(t,e,r,n,i={}){var o,s;eB(r);let a=i.extract||void 0;a&&!a.length&&(a=["none"]);let u=t.config(),c=(o=i,s=n,!(typeof File>"u")&&s instanceof File?Object.assign({filename:!1===o.preserveFilename?void 0:s.name,contentType:s.type},o):o),{tag:l,label:h,title:f,description:p,creditLine:d,filename:y,source:g}=c,m={label:h,title:f,description:p,filename:y,meta:a,creditLine:d};return g&&(m.sourceId=g.id,m.sourceName=g.name,m.sourceUrl=g.url),rz(t,e,{tag:l,method:"POST",timeout:c.timeout||0,uri:function(t,e){let r="image"===e?"images":"files";if(t["~experimental_resource"]){let{type:e,id:n}=t["~experimental_resource"];switch(e){case"dataset":throw Error("Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.");case"canvas":return`/canvases/${n}/assets/${r}`;case"media-library":return`/media-libraries/${n}/upload`;case"dashboard":return`/dashboards/${n}/assets/${r}`;default:throw Error(`Unsupported resource type: ${e.toString()}`)}}let n=eW(t);return`assets/${r}/${n}`}(u,r),headers:c.contentType?{"Content-Type":c.contentType}:{},query:m,body:n})}var r3=(t,e)=>Object.keys(e).concat(Object.keys(t)).reduce((r,n)=>(r[n]=typeof t[n]>"u"?e[n]:t[n],r),{});let r6=(t,e)=>e.reduce((e,r)=>(typeof t[r]>"u"||(e[r]=t[r]),e),{}),r5=t_(()=>r.e(406).then(r.t.bind(r,4406,19))).pipe(tz(({default:t})=>t),function(t,e,r){var n,i,o,s,a=!1;return s=null!=t?t:1/0,tZ({connector:function(){return new tX(s,e,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:a})}(1));function r8(){return function(t){return t.pipe(t0((t,e)=>{var r;return t instanceof e7?t2(tN({type:"reconnect"}),(void 0===r&&(r=t4),new tb(function(t){var e=1e3;e<0&&(e=0);var n=0;return r.schedule(function(){t.closed||(t.next(n++),t.complete())},e)})).pipe(tV(()=>e))):t9(()=>t)}))}}let r4=["includePreviousRevision","includeResult","includeMutations","includeAllVersions","visibility","effectFormat","tag"],r9={includeResult:!0};function r7(t,e,r={}){let{url:n,token:i,withCredentials:o,requestTagPrefix:s,headers:a}=this.config(),u=r.tag&&s?[s,r.tag].join("."):r.tag,c={...r3(r,r9),tag:u},l=ry({query:t,params:e,options:{tag:u,...r6(c,r4)}}),h=`${n}${rH(this,"listen",l)}`;if(h.length>14800)return t9(()=>Error("Query too large for listener"));let f=c.events?c.events:["mutation"],p={};return o&&(p.withCredentials=!0),(i||a)&&(p.headers={},i&&(p.headers.Authorization=`Bearer ${i}`),a&&Object.assign(p.headers,a)),ro(()=>(typeof EventSource>"u"||p.headers?r5:tN(EventSource)).pipe(tz(t=>new t(h,p))),f).pipe(r8(),es(t=>f.includes(t.type)),tz(t=>({type:t.type,..."data"in t?t.data:{}})))}let nt="2021-03-25";class ne{#a;constructor(t){this.#a=t}events({includeDrafts:t=!1,tag:e}={}){var r,n,i,o;eG("live",this.#a.config());let{projectId:s,apiVersion:a,token:u,withCredentials:c,requestTagPrefix:l,headers:h}=this.#a.config(),f=a.replace(/^v/,"");if("X"!==f&&f<nt)throw Error(`The live events API requires API version ${nt} or later. The current API version is ${f}. Please update your API version to use this feature.`);if(t&&!u&&!c)throw Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");let p=rH(this.#a,"live/events"),d=new URL(this.#a.getUrl(p,!1)),y=e&&l?[l,e].join("."):e;y&&d.searchParams.set("tag",y),t&&d.searchParams.set("includeDrafts","true");let g={};t&&c&&(g.withCredentials=!0),(t&&u||h)&&(g.headers={},t&&u&&(g.headers.Authorization=`Bearer ${u}`),h&&Object.assign(g.headers,h));let m=`${d.href}::${JSON.stringify(g)}`,v=nr.get(m);if(v)return v;let b=ro(()=>(typeof EventSource>"u"||g.headers?r5:tN(EventSource)).pipe(tz(t=>new t(d.href,g))),["message","restart","welcome","reconnect","goaway"]).pipe(r8(),tz(t=>{if("message"===t.type){let{data:e,...r}=t;return{...r,tags:e.tags}}return t})),w=t2((n=d,i={method:"OPTIONS",mode:"cors",credentials:g.withCredentials?"include":"omit",headers:g.headers},new tb(t=>{let e=new AbortController,r=e.signal;return fetch(n,{...i,signal:e.signal}).then(e=>{t.next(e),t.complete()},e=>{r.aborted||t.error(e)}),()=>e.abort()})).pipe(tV(()=>et),t0(()=>{throw new eS({projectId:s})})),b).pipe(t7(()=>nr.delete(m)),(o="function"==typeof(r={predicate:t=>"welcome"===t.type})?{predicate:r,...void 0}:r,t=>{var e,r,n,i;let s,a=!1,{predicate:u,...c}=o;return function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=t$(e),i=(t=1/0,"number"==typeof tT(e)?e.pop():t);return e.length?1===e.length?tS(e[0]):t1(i)(tL(e,n)):et}(t.pipe((i=W(e=t=>{o.predicate(t)&&(a=!0,s=t)})?{next:e,error:r,complete:n}:e)?tM(function(t,e){null==(r=i.subscribe)||r.call(i);var r,n=!0;t.subscribe(tU(e,function(t){var r;null==(r=i.next)||r.call(i,t),e.next(t)},function(){var t;n=!1,null==(t=i.complete)||t.call(i),e.complete()},function(t){var r;n=!1,null==(r=i.error)||r.call(i,t),e.error(t)},function(){var t,e;n&&(null==(t=i.unsubscribe)||t.call(i)),null==(e=i.finalize)||e.call(i)}))}):tm,t7(()=>{a=!1,s=void 0}),tZ(c)),new tb(t=>{a&&t.next(s),t.complete()}))}));return nr.set(m,w),w}}let nr=new Map;class nn{#a;#u;constructor(t,e){this.#a=t,this.#u=e}create(t,e){return no(this.#a,this.#u,"PUT",t,e)}edit(t,e){return no(this.#a,this.#u,"PATCH",t,e)}delete(t){return no(this.#a,this.#u,"DELETE",t)}list(){return rV(this.#a,this.#u,{uri:"/datasets",tag:null})}}class ni{#a;#u;constructor(t,e){this.#a=t,this.#u=e}create(t,e){return eG("dataset",this.#a.config()),tW(no(this.#a,this.#u,"PUT",t,e))}edit(t,e){return eG("dataset",this.#a.config()),tW(no(this.#a,this.#u,"PATCH",t,e))}delete(t){return eG("dataset",this.#a.config()),tW(no(this.#a,this.#u,"DELETE",t))}list(){return eG("dataset",this.#a.config()),tW(rV(this.#a,this.#u,{uri:"/datasets",tag:null}))}}function no(t,e,r,n,i){return eG("dataset",t.config()),eU(n),rV(t,e,{method:r,uri:`/datasets/${n}`,body:i,tag:null})}class ns{#a;#u;constructor(t,e){this.#a=t,this.#u=e}list(t){eG("projects",this.#a.config());let e=t?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return rV(this.#a,this.#u,{uri:e})}getById(t){return eG("projects",this.#a.config()),rV(this.#a,this.#u,{uri:`/projects/${t}`})}}class na{#a;#u;constructor(t,e){this.#a=t,this.#u=e}list(t){eG("projects",this.#a.config());let e=t?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return tW(rV(this.#a,this.#u,{uri:e}))}getById(t){return eG("projects",this.#a.config()),tW(rV(this.#a,this.#u,{uri:`/projects/${t}`}))}}let nu=((t,e=21)=>ew(t,e,eb))("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",8),nc=(t,e)=>e?eg(t,e):function(t){return ey(t)?ef+ev(t):ed(t)?t:ef+t}(t);function nl(t,{releaseId:e,publishedId:r,document:n}){if(r&&n._id){let t=nc(r,e);return eV(t,n),t}if(n._id){let r=ed(n._id),i=ey(n._id);if(!r&&!i)throw Error(`\`${t}()\` requires a document with an \`_id\` that is a version or draft ID`);if(e){if(r)throw Error(`\`${t}()\` was called with a document ID (\`${n._id}\`) that is a draft ID, but a release ID (\`${e}\`) was also provided.`);let i=em(n._id);if(i!==e)throw Error(`\`${t}()\` was called with a document ID (\`${n._id}\`) that is a version ID, but the release ID (\`${e}\`) does not match the document's version ID (\`${i}\`).`)}return n._id}if(r)return nc(r,e);throw Error(`\`${t}()\` requires either a publishedId or a document with an \`_id\``)}let nh=(t,e)=>{if("object"==typeof t&&null!==t&&("releaseId"in t||"metadata"in t)){let{releaseId:r=nu(),metadata:n={}}=t;return[r,n,e]}return[nu(),{},t]},nf=(t,e)=>{let[r,n,i]=nh(t,e);return{action:{actionType:"sanity.action.release.create",releaseId:r,metadata:{...n,releaseType:n.releaseType||"undecided"}},options:i}};class np{#a;#u;constructor(t,e){this.#a=t,this.#u=e}get({releaseId:t},e){return rC(this.#a,this.#u,`_.releases.${t}`,e)}create(t,e){let{action:r,options:n}=nf(t,e),{releaseId:i,metadata:o}=r;return r$(this.#a,this.#u,r,n).pipe(tz(t=>({...t,releaseId:i,metadata:o})))}edit({releaseId:t,patch:e},r){return r$(this.#a,this.#u,{actionType:"sanity.action.release.edit",releaseId:t,patch:e},r)}publish({releaseId:t},e){return r$(this.#a,this.#u,{actionType:"sanity.action.release.publish",releaseId:t},e)}archive({releaseId:t},e){return r$(this.#a,this.#u,{actionType:"sanity.action.release.archive",releaseId:t},e)}unarchive({releaseId:t},e){return r$(this.#a,this.#u,{actionType:"sanity.action.release.unarchive",releaseId:t},e)}schedule({releaseId:t,publishAt:e},r){return r$(this.#a,this.#u,{actionType:"sanity.action.release.schedule",releaseId:t,publishAt:e},r)}unschedule({releaseId:t},e){return r$(this.#a,this.#u,{actionType:"sanity.action.release.unschedule",releaseId:t},e)}delete({releaseId:t},e){return r$(this.#a,this.#u,{actionType:"sanity.action.release.delete",releaseId:t},e)}fetchDocuments({releaseId:t},e){return rR(this.#a,this.#u,t,e)}}class nd{#a;#u;constructor(t,e){this.#a=t,this.#u=e}get({releaseId:t},e){return tW(rC(this.#a,this.#u,`_.releases.${t}`,e))}async create(t,e){let{action:r,options:n}=nf(t,e),{releaseId:i,metadata:o}=r;return{...await tW(r$(this.#a,this.#u,r,n)),releaseId:i,metadata:o}}edit({releaseId:t,patch:e},r){return tW(r$(this.#a,this.#u,{actionType:"sanity.action.release.edit",releaseId:t,patch:e},r))}publish({releaseId:t},e){return tW(r$(this.#a,this.#u,{actionType:"sanity.action.release.publish",releaseId:t},e))}archive({releaseId:t},e){return tW(r$(this.#a,this.#u,{actionType:"sanity.action.release.archive",releaseId:t},e))}unarchive({releaseId:t},e){return tW(r$(this.#a,this.#u,{actionType:"sanity.action.release.unarchive",releaseId:t},e))}schedule({releaseId:t,publishAt:e},r){return tW(r$(this.#a,this.#u,{actionType:"sanity.action.release.schedule",releaseId:t,publishAt:e},r))}unschedule({releaseId:t},e){return tW(r$(this.#a,this.#u,{actionType:"sanity.action.release.unschedule",releaseId:t},e))}delete({releaseId:t},e){return tW(r$(this.#a,this.#u,{actionType:"sanity.action.release.delete",releaseId:t},e))}fetchDocuments({releaseId:t},e){return tW(rR(this.#a,this.#u,t,e))}}class ny{#a;#u;constructor(t,e){this.#a=t,this.#u=e}getById(t){return rV(this.#a,this.#u,{uri:`/users/${t}`})}}class ng{#a;#u;constructor(t,e){this.#a=t,this.#u=e}getById(t){return tW(rV(this.#a,this.#u,{uri:`/users/${t}`}))}}class nm{assets;datasets;live;projects;users;agent;releases;#c;#u;listen=r7;constructor(t,e=e6){this.config(e),this.#u=t,this.assets=new r0(this,this.#u),this.datasets=new nn(this,this.#u),this.live=new ne(this),this.projects=new ns(this,this.#u),this.users=new ny(this,this.#u),this.agent={action:new rZ(this,this.#u)},this.releases=new np(this,this.#u)}clone(){return new nm(this.#u,this.config())}config(t){if(void 0===t)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#c=e9(t,this.#c||{}),this}withConfig(t){let e=this.config();return new nm(this.#u,{...e,...t,stega:{...e.stega||{},..."boolean"==typeof t?.stega?{enabled:t.stega}:t?.stega||{}}})}fetch(t,e,r){return rE(this,this.#u,this.#c.stega,t,e,r)}getDocument(t,e){return rC(this,this.#u,t,e)}getDocuments(t,e){return rx(this,this.#u,t,e)}create(t,e){return rM(this,this.#u,t,"create",e)}createIfNotExists(t,e){return rO(this,this.#u,t,e)}createOrReplace(t,e){return rI(this,this.#u,t,e)}createVersion({document:t,publishedId:e,releaseId:r},n){let i=nl("createVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i},s=e||ev(t._id);return rA(this,this.#u,o,s,n)}delete(t,e){return rq(this,this.#u,t,e)}discardVersion({releaseId:t,publishedId:e},r,n){let i=nc(e,t);return rS(this,this.#u,i,r,n)}replaceVersion({document:t,publishedId:e,releaseId:r},n){let i=nl("replaceVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i};return rj(this,this.#u,o,n)}unpublishVersion({releaseId:t,publishedId:e},r){let n=eg(e,t);return r_(this,this.#u,n,e,r)}mutate(t,e){return rT(this,this.#u,t,e)}patch(t,e){return new rc(t,e,this)}transaction(t){return new rd(t,this)}action(t,e){return r$(this,this.#u,t,e)}request(t){return rV(this,this.#u,t)}getUrl(t,e){return rW(this,t,e)}getDataUrl(t,e){return rH(this,t,e)}}class nv{assets;datasets;live;projects;users;agent;releases;observable;#c;#u;listen=r7;constructor(t,e=e6){this.config(e),this.#u=t,this.assets=new r1(this,this.#u),this.datasets=new ni(this,this.#u),this.live=new ne(this),this.projects=new na(this,this.#u),this.users=new ng(this,this.#u),this.agent={action:new rK(this,this.#u)},this.releases=new nd(this,this.#u),this.observable=new nm(t,e)}clone(){return new nv(this.#u,this.config())}config(t){if(void 0===t)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(t),this.#c=e9(t,this.#c||{}),this}withConfig(t){let e=this.config();return new nv(this.#u,{...e,...t,stega:{...e.stega||{},..."boolean"==typeof t?.stega?{enabled:t.stega}:t?.stega||{}}})}fetch(t,e,r){return tW(rE(this,this.#u,this.#c.stega,t,e,r))}getDocument(t,e){return tW(rC(this,this.#u,t,e))}getDocuments(t,e){return tW(rx(this,this.#u,t,e))}create(t,e){return tW(rM(this,this.#u,t,"create",e))}createIfNotExists(t,e){return tW(rO(this,this.#u,t,e))}createOrReplace(t,e){return tW(rI(this,this.#u,t,e))}createVersion({document:t,publishedId:e,releaseId:r},n){let i=nl("createVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i},s=e||ev(t._id);return ee(rA(this,this.#u,o,s,n))}delete(t,e){return tW(rq(this,this.#u,t,e))}discardVersion({releaseId:t,publishedId:e},r,n){let i=nc(e,t);return tW(rS(this,this.#u,i,r,n))}replaceVersion({document:t,publishedId:e,releaseId:r},n){let i=nl("replaceVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i};return ee(rj(this,this.#u,o,n))}unpublishVersion({releaseId:t,publishedId:e},r){let n=eg(e,t);return tW(r_(this,this.#u,n,e,r))}mutate(t,e){return tW(rT(this,this.#u,t,e))}patch(t,e){return new rl(t,e,this)}transaction(t){return new rp(t,this)}action(t,e){return tW(r$(this,this.#u,t,e))}request(t){return tW(rV(this,this.#u,t))}dataRequest(t,e,r){return tW(rP(this,this.#u,t,e,r))}getUrl(t,e){return rW(this,t,e)}getDataUrl(t,e){return rH(this,t,e)}}let nb=function(t,e){return{requester:e_(t),createClient:r=>{let n=e_(t);return new e((t,e)=>(e||n)({maxRedirects:0,maxRetries:r.maxRetries,retryDelay:r.retryDelay,...t}),r)}}}([],nv),nw=(nb.requester,nb.createClient)},3786:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4612:function(t){t.exports=function(){function t(){return(t=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r="image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg";function n(t){return("image-"+t.split("/").slice(-1)[0]).replace(/\.([a-z]+)$/,"-$1")}var i=[["width","w"],["height","h"],["format","fm"],["download","dl"],["blur","blur"],["sharpen","sharp"],["invert","invert"],["orientation","or"],["minHeight","min-h"],["maxHeight","max-h"],["minWidth","min-w"],["maxWidth","max-w"],["quality","q"],["fit","fit"],["crop","crop"],["saturation","sat"],["auto","auto"],["dpr","dpr"],["pad","pad"],["frame","frame"]],o=["clip","crop","fill","fillmax","max","scale","min"],s=["top","bottom","left","right","center","focalpoint","entropy"],a=["format"],u=function(){function u(e,r){this.options=void 0,this.options=e?t({},e.options||{},r||{}):t({},r||{})}var c=u.prototype;return c.withOptions=function(r){var n=r.baseUrl||this.options.baseUrl,o={baseUrl:n};for(var s in r)r.hasOwnProperty(s)&&(o[function(t){for(var r,n=function(t,r){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,r){if(t){if("string"==typeof t)return e(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return e(t,void 0)}}(t))){n&&(t=n);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(i);!(r=n()).done;){var o=r.value,s=o[0],a=o[1];if(t===s||t===a)return s}return t}(s)]=r[s]);return new u(this,t({baseUrl:n},o))},c.image=function(t){return this.withOptions({source:t})},c.dataset=function(t){return this.withOptions({dataset:t})},c.projectId=function(t){return this.withOptions({projectId:t})},c.bg=function(t){return this.withOptions({bg:t})},c.dpr=function(t){return this.withOptions(t&&1!==t?{dpr:t}:{})},c.width=function(t){return this.withOptions({width:t})},c.height=function(t){return this.withOptions({height:t})},c.focalPoint=function(t,e){return this.withOptions({focalPoint:{x:t,y:e}})},c.maxWidth=function(t){return this.withOptions({maxWidth:t})},c.minWidth=function(t){return this.withOptions({minWidth:t})},c.maxHeight=function(t){return this.withOptions({maxHeight:t})},c.minHeight=function(t){return this.withOptions({minHeight:t})},c.size=function(t,e){return this.withOptions({width:t,height:e})},c.blur=function(t){return this.withOptions({blur:t})},c.sharpen=function(t){return this.withOptions({sharpen:t})},c.rect=function(t,e,r,n){return this.withOptions({rect:{left:t,top:e,width:r,height:n}})},c.format=function(t){return this.withOptions({format:t})},c.invert=function(t){return this.withOptions({invert:t})},c.orientation=function(t){return this.withOptions({orientation:t})},c.quality=function(t){return this.withOptions({quality:t})},c.forceDownload=function(t){return this.withOptions({download:t})},c.flipHorizontal=function(){return this.withOptions({flipHorizontal:!0})},c.flipVertical=function(){return this.withOptions({flipVertical:!0})},c.ignoreImageParams=function(){return this.withOptions({ignoreImageParams:!0})},c.fit=function(t){if(-1===o.indexOf(t))throw Error('Invalid fit mode "'+t+'"');return this.withOptions({fit:t})},c.crop=function(t){if(-1===s.indexOf(t))throw Error('Invalid crop mode "'+t+'"');return this.withOptions({crop:t})},c.saturation=function(t){return this.withOptions({saturation:t})},c.auto=function(t){if(-1===a.indexOf(t))throw Error('Invalid auto mode "'+t+'"');return this.withOptions({auto:t})},c.pad=function(t){return this.withOptions({pad:t})},c.vanityName=function(t){return this.withOptions({vanityName:t})},c.frame=function(t){if(1!==t)throw Error('Invalid frame value "'+t+'"');return this.withOptions({frame:t})},c.url=function(){return function(e){var o=t({},e||{}),s=o.source;delete o.source;var a=function(e){var r,i;if(!e)return null;if("string"==typeof e&&(i=e,/^https?:\/\//.test(""+i)))r={asset:{_ref:n(e)}};else if("string"==typeof e)r={asset:{_ref:e}};else if(e&&"string"==typeof e._ref)r={asset:e};else if(e&&"string"==typeof e._id)r={asset:{_ref:e._id||""}};else if(e&&e.asset&&"string"==typeof e.asset.url)r={asset:{_ref:n(e.asset.url)}};else{if("object"!=typeof e.asset)return null;r=t({},e)}return e.crop&&(r.crop=e.crop),e.hotspot&&(r.hotspot=e.hotspot),function(e){if(e.crop&&e.hotspot)return e;var r=t({},e);return r.crop||(r.crop={left:0,top:0,bottom:0,right:0}),r.hotspot||(r.hotspot={x:.5,y:.5,height:1,width:1}),r}(r)}(s);if(!a)throw Error("Unable to resolve image URL from source ("+JSON.stringify(s)+")");var u=function(t){var e=t.split("-"),n=e[1],i=e[2],o=e[3];if(!n||!i||!o)throw Error("Malformed asset _ref '"+t+"'. Expected an id like \""+r+'".');var s=i.split("x"),a=s[0],u=s[1],c=+a,l=+u;if(!(isFinite(c)&&isFinite(l)))throw Error("Malformed asset _ref '"+t+"'. Expected an id like \""+r+'".');return{id:n,width:c,height:l,format:o}}(a.asset._ref||a.asset._id||""),c=Math.round(a.crop.left*u.width),l=Math.round(a.crop.top*u.height),h={left:c,top:l,width:Math.round(u.width-a.crop.right*u.width-c),height:Math.round(u.height-a.crop.bottom*u.height-l)},f=a.hotspot.height*u.height/2,p=a.hotspot.width*u.width/2,d=a.hotspot.x*u.width,y=a.hotspot.y*u.height;return o.rect||o.focalPoint||o.ignoreImageParams||o.crop||(o=t({},o,function(t,e){var r,n=e.width,i=e.height;if(!(n&&i))return{width:n,height:i,rect:t.crop};var o=t.crop,s=t.hotspot,a=n/i;if(o.width/o.height>a){var u=Math.round(o.height),c=Math.round(u*a),l=Math.max(0,Math.round(o.top)),h=Math.max(0,Math.round(Math.round((s.right-s.left)/2+s.left)-c/2));h<o.left?h=o.left:h+c>o.left+o.width&&(h=o.left+o.width-c),r={left:h,top:l,width:c,height:u}}else{var f=o.width,p=Math.round(f/a),d=Math.max(0,Math.round(o.left)),y=Math.max(0,Math.round(Math.round((s.bottom-s.top)/2+s.top)-p/2));y<o.top?y=o.top:y+p>o.top+o.height&&(y=o.top+o.height-p),r={left:d,top:y,width:f,height:p}}return{width:n,height:i,rect:r}}({crop:h,hotspot:{left:d-p,top:y-f,right:d+p,bottom:y+f}},o))),function(t){var e=(t.baseUrl||"https://cdn.sanity.io").replace(/\/+$/,""),r=t.vanityName?"/"+t.vanityName:"",n=t.asset.id+"-"+t.asset.width+"x"+t.asset.height+"."+t.asset.format+r,o=e+"/images/"+t.projectId+"/"+t.dataset+"/"+n,s=[];if(t.rect){var a=t.rect,u=a.left,c=a.top,l=a.width,h=a.height;(0!==u||0!==c||h!==t.asset.height||l!==t.asset.width)&&s.push("rect="+u+","+c+","+l+","+h)}t.bg&&s.push("bg="+t.bg),t.focalPoint&&(s.push("fp-x="+t.focalPoint.x),s.push("fp-y="+t.focalPoint.y));var f=[t.flipHorizontal&&"h",t.flipVertical&&"v"].filter(Boolean).join("");return(f&&s.push("flip="+f),i.forEach(function(e){var r=e[0],n=e[1];void 0!==t[r]?s.push(n+"="+encodeURIComponent(t[r])):void 0!==t[n]&&s.push(n+"="+encodeURIComponent(t[n]))}),0===s.length)?o:o+"?"+s.join("&")}(t({},o,{asset:u}))}(this.options)},c.toString=function(){return this.url()},u}();return function(t){if(t&&"config"in t&&"function"==typeof t.config){var e=t.config(),r=e.apiHost,n=e.projectId,i=e.dataset;return new u(null,{baseUrl:(r||"https://api.sanity.io").replace(/^https:\/\/api\./,"https://cdn."),projectId:n,dataset:i})}if(t&&"clientConfig"in t&&"object"==typeof t.clientConfig){var o=t.clientConfig,s=o.apiHost,a=o.projectId,c=o.dataset;return new u(null,{baseUrl:(s||"https://api.sanity.io").replace(/^https:\/\/api\./,"https://cdn."),projectId:a,dataset:c})}return new u(null,t||{})}}()},6474:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6766:(t,e,r)=>{"use strict";r.d(e,{default:()=>i.a});var n=r(1469),i=r.n(n)},7863:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7924:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=u(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=u(t),s=o[0],a=o[1],c=new i((s+a)*3/4-a),l=0,h=a>0?s-4:s;for(r=0;r<h;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],c[l++]=e>>16&255,c[l++]=e>>8&255,c[l++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,c[l++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,c[l++]=e>>8&255,c[l++]=255&e),c},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(function(t,e,n){for(var i,o=[],s=e;s<n;s+=3)i=(t[s]<<16&0xff0000)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,s,s+16383>a?a:s+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function u(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(t,e,r){"use strict";var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return l(t)}return u(t,e,r)}function u(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|p(n,i),u=s(o),c=u.write(n,i);return c!==o&&(u=u.slice(0,c)),u}if(ArrayBuffer.isView(t))return h(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(S(t,ArrayBuffer)||t&&S(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(S(t,SharedArrayBuffer)||t&&S(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var l=t.valueOf&&t.valueOf();if(null!=l&&l!==t)return a.from(l,e,r);var d=function(t){if(a.isBuffer(t)){var e=0|f(t.length),r=s(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?s(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(d)return d;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function c(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return c(t),s(t<0?0:0|f(t))}function h(t){for(var e=t.length<0?0:0|f(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return u(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(c(t),t<=0)?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)},a.allocUnsafe=function(t){return l(t)},a.allocUnsafeSlow=function(t){return l(t)};function f(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||S(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return O(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return A(t).length;default:if(i)return n?-1:O(t).length;e=(""+e).toLowerCase(),i=!0}}function d(t,e,r){var i,o,s,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=j[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,o=e,s=r,0===o&&s===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function y(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function g(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:m(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return m(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function m(t,e,r,n,i){var o,s=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;s=2,a/=2,u/=2,r/=2}function c(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var l=-1;for(o=r;o<a;o++)if(c(t,o)===c(e,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*s}else -1!==l&&(o-=o-l),l=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var h=!0,f=0;f<u;f++)if(c(t,o+f)!==c(e,f)){h=!1;break}if(h)return o}return -1}a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(S(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),S(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(S(o,Uint8Array)&&(o=a.from(o)),!a.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?v(this,0,t):d.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(S(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,s=r-e,u=Math.min(o,s),c=this.slice(n,i),l=t.slice(e,r),h=0;h<u;++h)if(c[h]!==l[h]){o=c[h],s=l[h];break}return o<s?-1:+(s<o)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)};function v(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,u,c=t[i],l=null,h=c>239?4:c>223?3:c>191?2:1;if(i+h<=r)switch(h){case 1:c<128&&(l=c);break;case 2:(192&(o=t[i+1]))==128&&(u=(31&c)<<6|63&o)>127&&(l=u);break;case 3:o=t[i+1],s=t[i+2],(192&o)==128&&(192&s)==128&&(u=(15&c)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],(192&o)==128&&(192&s)==128&&(192&a)==128&&(u=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,h=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=h}var f=n,p=f.length;if(p<=4096)return String.fromCharCode.apply(String,f);for(var d="",y=0;y<p;)d+=String.fromCharCode.apply(String,f.slice(y,y+=4096));return d}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function w(t,e,r,n,i,o){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function E(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function C(t,e,r,n,o){return e*=1,r>>>=0,o||E(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function x(t,e,r,n,o){return e*=1,r>>>=0,o||E(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,a,u,c,l,h,f=this.length-e;if((void 0===r||r>f)&&(r=f),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a,u=parseInt(e.substr(2*s,2),16);if((a=u)!=a)break;t[r+s]=u}return s}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,q(O(t,this.length-i),this,i,o);case"ascii":return s=e,a=r,q(I(t),this,s,a);case"latin1":case"binary":return function(t,e,r,n){return q(I(e),t,r,n)}(this,t,e,r);case"base64":return u=e,c=r,q(A(t),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=e,h=r,q(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-l),this,l,h);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},a.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var o=1,s=0;for(this[e]=255&t;++s<r&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var o=r-1,s=1;for(this[e+o]=255&t;--o>=0&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return C(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return C(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return x(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return x(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var o=i-1;o>=0;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var s=a.isBuffer(t)?t:a.from(t,n),u=s.length;if(0===u)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=s[i%u]}return this};var R=/[^+/0-9A-Za-z-_]/g;function O(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function I(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function A(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(R,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function q(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function S(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var j=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,l=-7,h=r?i-1:0,f=r?-1:1,p=t[e+h];for(h+=f,o=p&(1<<-l)-1,p>>=-l,l+=a;l>0;o=256*o+t[e+h],h+=f,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+t[e+h],h+=f,l-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=c}return(p?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,u,c=8*o-i-1,l=(1<<c)-1,h=l>>1,f=5960464477539062e-23*(23===i),p=n?0:o-1,d=n?1:-1,y=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),s=l):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),s+h>=1?e+=f/u:e+=f*Math.pow(2,1-h),e*u>=2&&(s++,u/=2),s+h>=l?(a=0,s=l):s+h>=1?(a=(e*u-1)*Math.pow(2,i),s+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;t[r+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;t[r+p]=255&s,p+=d,s/=256,c-=8);t[r+p-d]|=128*y}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var o=r[t]={exports:{}},s=!0;try{e[t](o,o.exports,n),s=!1}finally{s&&delete r[t]}return o.exports}n.ab="//",t.exports=n(72)}()}}]);
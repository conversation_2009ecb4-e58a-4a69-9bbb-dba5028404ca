{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/GoogleAnalytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ndeclare global {\n  interface Window {\n    gtag: (...args: any[]) => void;\n    dataLayer: any[];\n  }\n}\n\nconst GoogleAnalytics = () => {\n  useEffect(() => {\n    // Only load Google Analytics in production and if GA_ID is provided\n    const GA_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n\n    if (!GA_ID || process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    // Check if scripts are already loaded\n    if (document.querySelector(`script[src*=\"gtag/js?id=${GA_ID}\"]`)) {\n      return;\n    }\n\n    // Load Google Analytics script\n    const script1 = document.createElement('script');\n    script1.async = true;\n    script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_ID}`;\n    document.head.appendChild(script1);\n\n    const script2 = document.createElement('script');\n    script2.innerHTML = `\n      window.dataLayer = window.dataLayer || [];\n      function gtag(){dataLayer.push(arguments);}\n      gtag('js', new Date());\n      gtag('config', '${GA_ID}');\n    `;\n    document.head.appendChild(script2);\n\n    // Make gtag available globally\n    window.gtag = function() {\n      window.dataLayer = window.dataLayer || [];\n      window.dataLayer.push(arguments);\n    };\n  }, []);\n\n  return null;\n};\n\nexport default GoogleAnalytics;\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAWA,MAAM,kBAAkB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,MAAM;QAEN,wCAAqD;YACnD;QACF;;QAOA,+BAA+B;QAC/B,MAAM;QAKN,MAAM;IAcR,GAAG,EAAE;IAEL,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/CrispChat.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ndeclare global {\n  interface Window {\n    $crisp: any[];\n    CRISP_WEBSITE_ID: string;\n  }\n}\n\ninterface CrispChatProps {\n  websiteId?: string;\n}\n\nconst CrispChat: React.FC<CrispChatProps> = ({ websiteId }) => {\n  useEffect(() => {\n    // Get website ID from props or environment variable\n    const crispWebsiteId = websiteId || process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID;\n    \n    if (!crispWebsiteId) {\n      console.warn('Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable.');\n      return;\n    }\n\n    // Initialize Crisp if not already loaded\n    if (typeof window !== 'undefined' && !window.$crisp) {\n      window.$crisp = [];\n      window.CRISP_WEBSITE_ID = crispWebsiteId;\n\n      // Create and append the Crisp script\n      const script = document.createElement('script');\n      script.src = 'https://client.crisp.chat/l.js';\n      script.async = true;\n      document.getElementsByTagName('head')[0].appendChild(script);\n\n      // Configure Crisp settings\n      window.$crisp.push(['safe', true]);\n      \n      // Set custom data for better support\n      window.$crisp.push(['set', 'user:company', 'Mobilify Website Visitor']);\n      window.$crisp.push(['set', 'session:data', {\n        source: 'website',\n        page: window.location.pathname,\n        timestamp: new Date().toISOString()\n      }]);\n\n      // Track chat interactions for analytics\n      window.$crisp.push(['on', 'chat:opened', () => {\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'chat_opened', {\n            event_category: 'engagement',\n            event_label: 'crisp_chat'\n          });\n        }\n      }]);\n\n      window.$crisp.push(['on', 'message:sent', () => {\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'chat_message_sent', {\n            event_category: 'engagement',\n            event_label: 'crisp_chat'\n          });\n        }\n      }]);\n\n      window.$crisp.push(['on', 'message:received', () => {\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'chat_message_received', {\n            event_category: 'engagement',\n            event_label: 'crisp_chat'\n          });\n        }\n      }]);\n    }\n\n    // Cleanup function\n    return () => {\n      // Note: Crisp doesn't provide a clean way to remove itself\n      // This is intentional as chat sessions should persist across page navigation\n    };\n  }, [websiteId]);\n\n  // This component doesn't render anything visible\n  // The chat widget is injected by Crisp's script\n  return null;\n};\n\nexport default CrispChat;\n\n// Utility functions for programmatic chat control\nexport const crispUtils = {\n  // Open the chat widget\n  openChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:open']);\n    }\n  },\n\n  // Close the chat widget\n  closeChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:close']);\n    }\n  },\n\n  // Show the chat widget\n  showChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:show']);\n    }\n  },\n\n  // Hide the chat widget\n  hideChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:hide']);\n    }\n  },\n\n  // Set user information\n  setUser: (user: { nickname?: string; email?: string; phone?: string; avatar?: string }) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      if (user.nickname) window.$crisp.push(['set', 'user:nickname', user.nickname]);\n      if (user.email) window.$crisp.push(['set', 'user:email', user.email]);\n      if (user.phone) window.$crisp.push(['set', 'user:phone', user.phone]);\n      if (user.avatar) window.$crisp.push(['set', 'user:avatar', user.avatar]);\n    }\n  },\n\n  // Send a message programmatically\n  sendMessage: (message: string) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'message:send', ['text', message]]);\n    }\n  },\n\n  // Set session data\n  setSessionData: (data: Record<string, any>) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['set', 'session:data', data]);\n    }\n  },\n\n  // Check if chat is available\n  isChatAvailable: (): boolean => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      return window.$crisp.length > 0;\n    }\n    return false;\n  },\n\n  // Set custom segments for targeting\n  setSegments: (segments: string[]) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['set', 'session:segments', segments]);\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAeA,MAAM,YAAsC,CAAC,EAAE,SAAS,EAAE;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,MAAM,iBAAiB,aAAa,QAAQ,GAAG,CAAC,4BAA4B;QAE5E,IAAI,CAAC,gBAAgB;YACnB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,yCAAyC;QACzC,uCAAqD;;QAgDrD;QAEA,mBAAmB;QACnB,OAAO;QACL,2DAA2D;QAC3D,6EAA6E;QAC/E;IACF,GAAG;QAAC;KAAU;IAEd,iDAAiD;IACjD,gDAAgD;IAChD,OAAO;AACT;uCAEe;AAGR,MAAM,aAAa;IACxB,uBAAuB;IACvB,UAAU;QACR,uCAAoD;;QAEpD;IACF;IAEA,wBAAwB;IACxB,WAAW;QACT,uCAAoD;;QAEpD;IACF;IAEA,uBAAuB;IACvB,UAAU;QACR,uCAAoD;;QAEpD;IACF;IAEA,uBAAuB;IACvB,UAAU;QACR,uCAAoD;;QAEpD;IACF;IAEA,uBAAuB;IACvB,SAAS,CAAC;QACR,uCAAoD;;QAKpD;IACF;IAEA,kCAAkC;IAClC,aAAa,CAAC;QACZ,uCAAoD;;QAEpD;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC;QACf,uCAAoD;;QAEpD;IACF;IAEA,6BAA6B;IAC7B,iBAAiB;QACf,uCAAoD;;QAEpD;QACA,OAAO;IACT;IAEA,oCAAoC;IACpC,aAAa,CAAC;QACZ,uCAAoD;;QAEpD;IACF;AACF", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [theme, setThemeState] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('mobilify-theme') as Theme;\n    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    const initialTheme = savedTheme || systemTheme;\n    \n    setThemeState(initialTheme);\n    setMounted(true);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = document.documentElement;\n    \n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n\n    // Save to localStorage\n    localStorage.setItem('mobilify-theme', theme);\n\n    // Track theme change for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'theme_changed', {\n        event_category: 'user_preference',\n        event_label: theme\n      });\n    }\n  }, [theme, mounted]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    if (!mounted) return;\n\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = (e: MediaQueryListEvent) => {\n      // Only auto-switch if user hasn't manually set a preference\n      const savedTheme = localStorage.getItem('mobilify-theme');\n      if (!savedTheme) {\n        setThemeState(e.matches ? 'dark' : 'light');\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [mounted]);\n\n  const toggleTheme = () => {\n    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>\n      <div suppressHydrationWarning>\n        {children}\n      </div>\n    </ThemeContext.Provider>\n  );\n};\n\n// Hook for getting system theme preference\nexport const useSystemTheme = (): Theme => {\n  const [systemTheme, setSystemTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setSystemTheme(e.matches ? 'dark' : 'light');\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  return systemTheme;\n};\n\n// Utility function to get theme-aware classes\nexport const getThemeClasses = (lightClasses: string, darkClasses: string) => {\n  return `${lightClasses} dark:${darkClasses}`;\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;QACzF,MAAM,eAAe,cAAc;QAEnC,cAAc;QACd,WAAW;IACb,GAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,OAAO,SAAS,eAAe;QAErC,IAAI,UAAU,QAAQ;YACpB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;QAEA,uBAAuB;QACvB,aAAa,OAAO,CAAC,kBAAkB;QAEvC,mCAAmC;QACnC,uCAA2D;;QAK3D;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,aAAa,OAAO,UAAU,CAAC;QAErC,MAAM,eAAe,CAAC;YACpB,4DAA4D;YAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,CAAC,YAAY;gBACf,cAAc,EAAE,OAAO,GAAG,SAAS;YACrC;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;KAAQ;IAEZ,MAAM,cAAc;QAClB,cAAc,CAAA,YAAa,cAAc,UAAU,SAAS;IAC9D;IAEA,MAAM,WAAW,CAAC;QAChB,cAAc;IAChB;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAa;QAAS;kBAC3D,cAAA,8OAAC;YAAI,wBAAwB;sBAC1B;;;;;;;;;;;AAIT;AAGO,MAAM,iBAAiB;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,eAAe,WAAW,OAAO,GAAG,SAAS;QAE7C,MAAM,eAAe,CAAC;YACpB,eAAe,EAAE,OAAO,GAAG,SAAS;QACtC;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,MAAM,kBAAkB,CAAC,cAAsB;IACpD,OAAO,GAAG,aAAa,MAAM,EAAE,aAAa;AAC9C", "debugId": null}}]}
# GROQ-JS<!-- omit in toc -->

[![npm stat](https://img.shields.io/npm/dm/groq-js.svg?style=flat-square)](https://npm-stat.com/charts.html?package=groq-js)
[![npm version](https://img.shields.io/npm/v/groq-js.svg?style=flat-square)](https://www.npmjs.com/package/groq-js)
[![gzip size][gzip-badge]][bundlephobia]
[![size][size-badge]][bundlephobia]

GROQ-JS is a JavaScript implementation of [GROQ](https://www.sanity.io/docs/how-queries-work) which follows the official specification.

```javascript
import {parse, evaluate} from 'groq-js'

let input = '*[_type == "user"]{name}'

// Returns an ESTree-inspired syntax tree
let tree = parse(input)

let dataset = [
  {_type: 'user', name: '<PERSON>'},
  {_type: 'company', name: 'Bluth Company'},
]

// Evaluate a tree against a dataset
let value = await evaluate(tree, {dataset})

// Gather everything into one JavaScript object
let result = await value.get()

console.log(result)
```

Table of contents:

- [Installation](#installation)
- [Documentation](#documentation)
- [Learn GROQ](#learn-groq)
- [Versioning](#versioning)
  - [GROQ](#groq)
  - [GROQ-JS](#groq-js-1)
- [Releasing a new version of GROQ-JS](#releasing-a-new-version-of-groq-js)
- [License](#license)
- [Tests](#tests)

## Installation

```bash
npm i groq-js
```

```bash
yarn add groq-js
```

```bash
pnpm install groq-js
```

## Documentation

See [API.md](API.md) for the public API.

## Learn GROQ

[![Free egghead GROQ introduction course by John Lindquist](https://user-images.githubusercontent.com/6188161/*********-fc04ac47-d0fa-492b-897b-4203c97e94ec.png)](https://egghead.io/courses/introduction-to-groq-query-language-6e9c6fc0?utm_source=github&utm_medium=cta&utm_term=GROQ)

## Versioning

### GROQ

The GROQ spec version is independent of the groq-js library version. When you import groq-js you need to be explicit on which GROQ version you want to use. The GROQ version is tied to the [groq-spec](https://github.com/sanity-io/groq). This allows us to update the library and its API independent of the GROQ version.

### GROQ-JS

GROQ-JS follows [SemVer](https://semver.org).
See [the changelog](CHANGELOG.md) for recent changes.
This is an "experimental" release and anything _may_ change at any time, but we're trying to keep changes as minimal as possible:

- The public API of the parser/evaluator will most likely stay the same in future versions.
- The syntax tree is _not_ considered a public API and may change at any time.
- This package always implements the latest version of [GROQ according to the specification](https://github.com/sanity-io/groq).

## Releasing a new version of GROQ-JS

Run the ["CI & Release" workflow](https://github.com/sanity-io/groq-js/actions/workflows/test.yml). Make sure to select the main branch and check "Release new version".

Version will be automatically bumped based on [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/) since the last release.

Semantic release will only release on configured branches, so it is safe to run release on any branch.

Note: commits with `chore:` will be ignored. If you want updated dependencies to trigger
a new version, use `fix(deps):` instead.

## License

MIT © [Sanity.io](https://www.sanity.io/)

## Tests

Tests are written in [Jest](https://jestjs.io/):

```bash
# Install dependencies
npm i

# Run tests
npm test
```

You can also generate tests from [the official GROQ test suite](https://github.com/sanity-io/groq-test-suite):

```bash
# Fetch and generate test file:
./test/generate.sh

# Run tests as usual:
npm test
```

You can generate tests from a specific version:

```shell
GROQTEST_SUITE_VERSION=v1.0.0 ./test/generate.sh
```

or from a file (as generated by the test suite):

```shell
GROQTEST_SUITE=suite.ndjson ./test/generate.sh
```

The test arguments are passed to `tap`, so you can use arguments, e.g. to run a specific set of tests:

```shell
npm test -g "array::join"
```

[gzip-badge]: https://img.shields.io/bundlephobia/minzip/groq-js?label=gzip%20size&style=flat-square
[size-badge]: https://img.shields.io/bundlephobia/min/groq-js?label=size&style=flat-square
[bundlephobia]: https://bundlephobia.com/package/groq-js

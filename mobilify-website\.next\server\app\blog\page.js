(()=>{var e={};e.id=831,e.ids=[831],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11639:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>u});var r=s(37413);s(61120);var i=s(4536),o=s.n(i),l=s(53384),n=s(36440),a=s(88971),c=s(40918),d=s(24597),p=s(18512),m=s(97569);let u={title:"Blog | Mobilify - Mobile App Development Insights",description:"Get the latest insights on mobile app development, industry trends, and tips for turning your ideas into successful mobile apps.",openGraph:{title:"Blog | Mobilify - Mobile App Development Insights",description:"Get the latest insights on mobile app development, industry trends, and tips for turning your ideas into successful mobile apps.",type:"website"}};async function x({searchParams:e}){let{category:t}=await e,s=[{_id:"mobile-dev",_type:"category",title:"Mobile Development",slug:{current:"mobile-development"},description:"Tips and tutorials for mobile app development"},{_id:"industry-news",_type:"category",title:"Industry News",slug:{current:"industry-news"},description:"Latest trends and news in the mobile industry"},{_id:"startup-tips",_type:"category",title:"Startup Tips",slug:{current:"startup-tips"},description:"Advice for entrepreneurs building mobile apps"}],i=[{_id:"1",_type:"post",title:"5 Essential Features Every Mobile App Needs",slug:{current:"5-essential-features-every-mobile-app-needs"},author:"Mobilify Team",mainImage:void 0,categories:[s[0]],publishedAt:"2024-01-15T10:00:00Z",excerpt:"Discover the must-have features that make mobile apps successful and keep users engaged.",_createdAt:"2024-01-15T10:00:00Z"},{_id:"2",_type:"post",title:"The Future of Mobile App Development in 2024",slug:{current:"future-of-mobile-app-development-2024"},author:"Mobilify Team",mainImage:void 0,categories:[s[1]],publishedAt:"2024-01-10T14:30:00Z",excerpt:"Explore the latest trends and technologies shaping the mobile app development landscape.",_createdAt:"2024-01-10T14:30:00Z"},{_id:"3",_type:"post",title:"From Idea to App Store: A Startup's Journey",slug:{current:"from-idea-to-app-store-startup-journey"},author:"Mobilify Team",mainImage:void 0,categories:[s[2]],publishedAt:"2024-01-05T09:15:00Z",excerpt:"Follow the complete journey of turning a startup idea into a successful mobile app.",_createdAt:"2024-01-05T09:15:00Z"}],u=t?i.filter(e=>e.categories.some(e=>e.slug.current===t)):i,x=t?s.find(e=>e.slug.current===t):null,h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,r.jsxs)("div",{className:"min-h-screen w-full overflow-x-hidden",children:[(0,r.jsx)(d.default,{}),(0,r.jsxs)("main",{className:"pt-16",children:[(0,r.jsx)("section",{className:"py-16 bg-gradient-to-r from-electric-blue to-indigo-600",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:x?x.title:"Mobile App Insights"}),(0,r.jsx)("p",{className:"text-xl text-blue-100 max-w-3xl mx-auto",children:x?x.description||`Articles about ${x.title.toLowerCase()}`:"Discover the latest trends, tips, and insights in mobile app development"})]})}),(0,r.jsx)("section",{className:"py-8 bg-white border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsx)(o(),{href:"/blog",className:`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${!t?"bg-electric-blue text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"All Posts"}),s.map(e=>(0,r.jsx)(o(),{href:`/blog?category=${e.slug.current}`,className:`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${t===e.slug.current?"bg-electric-blue text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:e.title},e._id))]})})}),(0,r.jsx)("section",{className:"py-16",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:0===u.length?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-dark-charcoal mb-4",children:"No posts found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:x?`No posts found in the ${x.title} category.`:"No blog posts are available at the moment."}),(0,r.jsxs)(o(),{href:"/blog",className:"inline-flex items-center bg-electric-blue text-white px-6 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-200",children:["View All Posts",(0,r.jsx)(n.A,{className:"ml-2 w-5 h-5"})]})]}):(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:u.map((e,t)=>(0,r.jsxs)("article",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden",children:[e.mainImage&&(0,r.jsx)("div",{className:"relative h-48 w-full",children:(0,r.jsx)(l.default,{src:(0,m.dk)(e.mainImage).width(400).height(200).url(),alt:e.mainImage.alt||e.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-3",children:e.categories.map(e=>(0,r.jsx)(o(),{href:`/blog?category=${e.slug.current}`,className:"text-xs font-medium text-electric-blue bg-blue-50 px-2 py-1 rounded-full hover:bg-blue-100 transition-colors duration-200",children:e.title},e._id))}),(0,r.jsx)("h2",{className:"text-xl font-bold text-dark-charcoal mb-3 line-clamp-2",children:(0,r.jsx)(o(),{href:`/blog/${e.slug.current}`,className:"hover:text-electric-blue transition-colors duration-200",children:e.title})}),e.excerpt&&(0,r.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.excerpt}),(0,r.jsx)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(a.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.author})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:h(e.publishedAt)})]})]})}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,r.jsxs)(o(),{href:`/blog/${e.slug.current}`,className:"inline-flex items-center text-electric-blue font-medium hover:underline",children:["Read More",(0,r.jsx)(n.A,{className:"ml-1 w-4 h-4"})]})})]})]},e._id))})})})]}),(0,r.jsx)(p.A,{})]})}},18512:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(37413);s(61120);let i=({className:e=""})=>(0,r.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})});var o=s(68367),l=s(38435);let n=()=>(0,r.jsx)("footer",{className:"bg-gray-900 dark:bg-gray-950 text-white py-12 transition-colors duration-300",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(i,{}),(0,r.jsx)("span",{className:"ml-2 text-xl font-bold",children:"Mobilify"})]}),(0,r.jsx)("p",{className:"text-gray-400 text-sm leading-relaxed",children:"Transforming ideas into mobile reality. We help entrepreneurs and businesses create beautiful, high-performance mobile apps without the traditional complexity and cost."})]}),(0,r.jsxs)("div",{className:"lg:col-span-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/#demo",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"See Demo"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/services",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"Services & Pricing"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/about",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"About Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/#contact",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"Contact"})})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)(o.default,{variant:"footer"})})]}),(0,r.jsx)("div",{className:"border-t border-gray-800 pt-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 Mobilify. All rights reserved."}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 mt-4 md:mt-0",children:[(0,r.jsx)(l.DarkModeToggles.FooterToggle,{}),(0,r.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors duration-200",children:"Privacy Policy"}),(0,r.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors duration-200",children:"Terms of Service"})]})]})})]})})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24597:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Header.tsx","default")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38435:(e,t,s)=>{"use strict";s.d(t,{DarkModeToggles:()=>i});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\DarkModeToggle.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx","default");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call DarkModeToggles() from the server but DarkModeToggles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx","DarkModeToggles")},53726:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.t.bind(s,46533,23)),Promise.resolve().then(s.bind(s,52797)),Promise.resolve().then(s.bind(s,35899)),Promise.resolve().then(s.bind(s,91477))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58454:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.t.bind(s,49603,23)),Promise.resolve().then(s.bind(s,38435)),Promise.resolve().then(s.bind(s,24597)),Promise.resolve().then(s.bind(s,68367))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68367:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\NewsletterSignup.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx","default")},79422:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),i=s(48088),o=s(88170),l=s.n(o),n=s(30893),a={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>n[e]);s.d(t,a);let c={children:["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,11639)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},97569:(e,t,s)=>{"use strict";s.d(t,{dk:()=>a,x8:()=>m,zX:()=>p});var r=s(25588),i=s(23777),o=s.n(i);let l=(0,r.UU)({projectId:process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,dataset:process.env.NEXT_PUBLIC_SANITY_DATASET||"production",apiVersion:"2024-01-01",useCdn:!0,token:process.env.SANITY_API_TOKEN}),n=o()(l);function a(e){return n.image(e)}let c=`*[_type == "post" && defined(slug.current)] | order(publishedAt desc) {
  _id,
  title,
  slug,
  author,
  mainImage,
  categories[]-> {
    _id,
    title,
    slug
  },
  publishedAt,
  excerpt,
  _createdAt
}`,d=`*[_type == "post" && _id != $postId && count(categories[@._ref in $categoryIds]) > 0] | order(publishedAt desc)[0...3] {
  _id,
  title,
  slug,
  mainImage,
  publishedAt,
  excerpt
}`;async function p(){return await l.fetch(c)}async function m(e,t){return await l.fetch(d,{postId:e,categoryIds:t})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,71,276,814,532,533,331,225,935],()=>s(79422));module.exports=r})();
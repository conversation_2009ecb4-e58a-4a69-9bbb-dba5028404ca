"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[27],{637:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(5155);a(2115);let i=e=>{let{className:t=""}=e;return(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ".concat(t),children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})})}},4615:(e,t,a)=>{a.d(t,{default:()=>h});var s=a(5155),i=a(2115),r=a(4416),n=a(4783),l=a(637),o=a(4843),c=a(9437),d=a(5925);let h=()=>{let[e,t]=(0,i.useState)(!1),a=e=>{let a=document.getElementById(e);a&&a.scrollIntoView({behavior:"smooth"}),t(!1)},h=[{label:"Services",href:"#services-overview"},{label:"How It Works",href:"#process"},{label:"About Us",href:"#about"}];return(0,s.jsxs)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300",children:[(0,s.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("span",{className:"ml-2 text-xl font-bold text-dark-charcoal dark:text-white",children:"Mobilify"})]}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[h.map(e=>(0,s.jsx)("button",{onClick:()=>a(e.href.substring(1)),className:"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200",children:e.label},e.label)),(0,s.jsx)(c.ChatTriggers.HeaderChat,{}),(0,s.jsx)(o.default,{children:(0,s.jsx)(d.DarkModeToggles.HeaderToggle,{})}),(0,s.jsx)("button",{onClick:()=>a("contact"),className:"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200",children:"Get a Quote"})]}),(0,s.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,s.jsx)(r.A,{size:24}):(0,s.jsx)(n.A,{size:24})})]})}),(0,s.jsx)(o.default,{children:e&&(0,s.jsx)("div",{className:"md:hidden fixed inset-0 top-16 bg-white z-40",children:(0,s.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[h.map(e=>(0,s.jsx)("button",{onClick:()=>a(e.href.substring(1)),className:"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2",children:e.label},e.label)),(0,s.jsx)("button",{onClick:()=>a("contact"),className:"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6",children:"Get a Quote"})]})})})]})}},4843:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var s=a(5155),i=a(2115);let r=e=>{let{children:t,fallback:a=null}=e,[r,n]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{n(!0)},[]),r)?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)(s.Fragment,{children:a})}},5925:(e,t,a)=>{a.d(t,{DarkModeToggles:()=>c});var s=a(5155);a(2115);var i=a(3509),r=a(2098),n=a(6408),l=a(7740);let o=e=>{let{variant:t="button",size:a="md",className:o="",showLabel:c=!1}=e,{theme:d,toggleTheme:h}=(0,l.DP)(),g=()=>{switch(a){case"sm":return"w-4 h-4";case"lg":return"w-6 h-6";default:return"w-5 h-5"}};return"switch"===t?(0,s.jsxs)("div",{className:"flex items-center gap-3 ".concat(o),children:[c&&(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["dark"===d?"Dark":"Light"," Mode"]}),(0,s.jsx)("button",{onClick:h,className:"relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 dark:focus:ring-offset-gray-800",role:"switch","aria-checked":"dark"===d,"aria-label":"Switch to ".concat("dark"===d?"light":"dark"," mode"),children:(0,s.jsx)(n.P.span,{className:"inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-200",animate:{x:"dark"===d?24:4},transition:{type:"spring",stiffness:500,damping:30},children:(0,s.jsx)(n.P.div,{className:"flex items-center justify-center h-full w-full",initial:!1,animate:{rotate:180*("dark"===d)},transition:{duration:.2},children:"dark"===d?(0,s.jsx)(i.A,{className:"w-2.5 h-2.5 text-gray-600"}):(0,s.jsx)(r.A,{className:"w-2.5 h-2.5 text-yellow-500"})})})})]}):(0,s.jsxs)("div",{className:"flex items-center gap-2 ".concat(o),children:[(0,s.jsx)(n.P.button,{onClick:h,className:"\n          ".concat((()=>{switch(a){case"sm":return"w-8 h-8";case"lg":return"w-12 h-12";default:return"w-10 h-10"}})(),"\n          flex items-center justify-center\n          rounded-lg\n          bg-gray-100 hover:bg-gray-200\n          dark:bg-gray-800 dark:hover:bg-gray-700\n          text-gray-600 dark:text-gray-300\n          transition-colors duration-200\n          focus:outline-none focus:ring-2 focus:ring-electric-blue focus:ring-offset-2\n          dark:focus:ring-offset-gray-800\n        "),whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Switch to ".concat("dark"===d?"light":"dark"," mode"),title:"Switch to ".concat("dark"===d?"light":"dark"," mode"),children:(0,s.jsx)(n.P.div,{initial:!1,animate:{rotate:180*("dark"===d),scale:"dark"===d?.8:1},transition:{duration:.3,ease:"easeInOut"},children:"dark"===d?(0,s.jsx)(r.A,{className:"".concat(g()," text-yellow-400")}):(0,s.jsx)(i.A,{className:"".concat(g()," text-gray-600")})})}),c&&(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["dark"===d?"Light":"Dark"," Mode"]})]})},c={HeaderToggle:()=>(0,s.jsx)(o,{variant:"button",size:"sm",className:"mr-4"}),SettingsToggle:()=>(0,s.jsx)(o,{variant:"switch",size:"md",showLabel:!0,className:"justify-between w-full"}),MobileToggle:()=>(0,s.jsx)(o,{variant:"button",size:"md",showLabel:!0,className:"justify-center"}),FooterToggle:()=>(0,s.jsx)(o,{variant:"switch",size:"sm"})}},6129:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=a(5155),i=a(2115),r=a(6408),n=a(646),l=a(5339),o=a(8883);let c=e=>{let{variant:t="footer",className:a=""}=e,[c,d]=(0,i.useState)(""),[h,g]=(0,i.useState)("idle"),[u,m]=(0,i.useState)(""),x=async e=>{if(e.preventDefault(),!c.trim()){g("error"),m("Please enter a valid email address");return}g("loading");try{let e=await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:c})}),a=await e.json();e.ok?(g("success"),m("Thanks for subscribing! Check your email for confirmation."),d(""),window.gtag&&window.gtag("event","newsletter_signup",{event_category:"engagement",event_label:t})):(g("error"),m(a.error||"Something went wrong. Please try again."))}catch(e){g("error"),m("Network error. Please check your connection and try again.")}setTimeout(()=>{g("idle"),m("")},5e3)};return"footer"===t?(0,s.jsxs)("div",{className:"space-y-4 ".concat(a),children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-dark-charcoal",children:"Stay Updated"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Get the latest mobile app development tips and Mobilify updates."}),(0,s.jsxs)("form",{onSubmit:x,className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,s.jsx)("input",{type:"email",value:c,onChange:e=>d(e.target.value),placeholder:"Enter your email",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-transparent text-sm",disabled:"loading"===h}),(0,s.jsx)("button",{type:"submit",disabled:"loading"===h||!c.trim(),className:"bg-electric-blue text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 text-sm whitespace-nowrap",children:"loading"===h?"Subscribing...":"Subscribe"})]}),u&&(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"flex items-center gap-2 text-sm ".concat("success"===h?"text-green-600":"text-red-600"),children:["success"===h?(0,s.jsx)(n.A,{className:"w-4 h-4"}):(0,s.jsx)(l.A,{className:"w-4 h-4"}),u]})]})]}):(0,s.jsx)("section",{className:"py-16 bg-gradient-to-r from-electric-blue to-indigo-600 ".concat(a),children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"bg-white/20 rounded-full p-3",children:(0,s.jsx)(o.A,{className:"w-8 h-8 text-white"})})}),(0,s.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-white",children:"Get Mobile App Insights"}),(0,s.jsx)("p",{className:"text-xl text-blue-100 max-w-2xl mx-auto",children:"Join 500+ entrepreneurs getting weekly tips on mobile app development, industry trends, and exclusive Mobilify updates."}),(0,s.jsxs)("form",{onSubmit:x,className:"max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsx)("input",{type:"email",value:c,onChange:e=>d(e.target.value),placeholder:"Enter your email address",className:"flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-electric-blue text-dark-charcoal",disabled:"loading"===h}),(0,s.jsx)("button",{type:"submit",disabled:"loading"===h||!c.trim(),className:"bg-white text-electric-blue px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 whitespace-nowrap",children:"loading"===h?"Subscribing...":"Get Updates"})]}),u&&(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mt-4 flex items-center justify-center gap-2 ".concat("success"===h?"text-green-100":"text-red-100"),children:["success"===h?(0,s.jsx)(n.A,{className:"w-5 h-5"}):(0,s.jsx)(l.A,{className:"w-5 h-5"}),u]})]}),(0,s.jsx)("p",{className:"text-blue-200 text-sm",children:"No spam, ever. Unsubscribe with one click."})]})})})}},7740:(e,t,a)=>{a.d(t,{DP:()=>n,ThemeProvider:()=>l});var s=a(5155),i=a(2115);let r=(0,i.createContext)(void 0),n=()=>{let e=(0,i.useContext)(r);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},l=e=>{let{children:t}=e,[a,n]=(0,i.useState)("light"),[l,o]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(e||t),o(!0)},[]),(0,i.useEffect)(()=>{if(!l)return;let e=document.documentElement;"dark"===a?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",a),window.gtag&&window.gtag("event","theme_changed",{event_category:"user_preference",event_label:a})},[a,l]),(0,i.useEffect)(()=>{if(!l)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||n(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[l]),(0,s.jsx)(r.Provider,{value:{theme:a,toggleTheme:()=>{n(e=>"light"===e?"dark":"light")},setTheme:e=>{n(e)}},children:(0,s.jsx)("div",{suppressHydrationWarning:!0,children:t})})}},8751:(e,t,a)=>{a.d(t,{default:()=>r,l:()=>n});var s=a(2115),i=a(9509);let r=e=>{let{websiteId:t}=e;return(0,s.useEffect)(()=>{let e=t||i.env.NEXT_PUBLIC_CRISP_WEBSITE_ID;if(!e)return void console.warn("Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable.");if(!window.$crisp){window.$crisp=[],window.CRISP_WEBSITE_ID=e;let t=document.createElement("script");t.src="https://client.crisp.chat/l.js",t.async=!0,document.getElementsByTagName("head")[0].appendChild(t),window.$crisp.push(["safe",!0]),window.$crisp.push(["set","user:company","Mobilify Website Visitor"]),window.$crisp.push(["set","session:data",{source:"website",page:window.location.pathname,timestamp:new Date().toISOString()}]),window.$crisp.push(["on","chat:opened",()=>{window.gtag&&window.gtag("event","chat_opened",{event_category:"engagement",event_label:"crisp_chat"})}]),window.$crisp.push(["on","message:sent",()=>{window.gtag&&window.gtag("event","chat_message_sent",{event_category:"engagement",event_label:"crisp_chat"})}]),window.$crisp.push(["on","message:received",()=>{window.gtag&&window.gtag("event","chat_message_received",{event_category:"engagement",event_label:"crisp_chat"})}])}return()=>{}},[t]),null},n={openChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:open"])},closeChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:close"])},showChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:show"])},hideChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:hide"])},setUser:e=>{window.$crisp&&(e.nickname&&window.$crisp.push(["set","user:nickname",e.nickname]),e.email&&window.$crisp.push(["set","user:email",e.email]),e.phone&&window.$crisp.push(["set","user:phone",e.phone]),e.avatar&&window.$crisp.push(["set","user:avatar",e.avatar]))},sendMessage:e=>{window.$crisp&&window.$crisp.push(["do","message:send",["text",e]])},setSessionData:e=>{window.$crisp&&window.$crisp.push(["set","session:data",e])},isChatAvailable:()=>!!window.$crisp&&window.$crisp.length>0,setSegments:e=>{window.$crisp&&window.$crisp.push(["set","session:segments",e])}}},9437:(e,t,a)=>{a.d(t,{ChatTriggers:()=>d});var s=a(5155);a(2115);var i=a(9947),r=a(9420),n=a(1366),l=a(6408),o=a(8751);let c=e=>{let{variant:t="button",text:a="Chat with us",icon:c="message",className:d="",size:h="md",context:g="general"}=e,u=()=>{o.l.setSessionData({trigger_context:g,trigger_page:window.location.pathname,trigger_time:new Date().toISOString()}),o.l.openChat(),window.gtag&&window.gtag("event","chat_trigger_clicked",{event_category:"engagement",event_label:g,custom_parameter_1:t})},m=()=>{let e={className:"sm"===h?"w-4 h-4":"lg"===h?"w-6 h-6":"w-5 h-5"};switch(c){case"help":return(0,s.jsx)(i.A,{...e});case"phone":return(0,s.jsx)(r.A,{...e});default:return(0,s.jsx)(n.A,{...e})}};return"floating"===t?(0,s.jsxs)(l.P.button,{onClick:u,className:"fixed bottom-6 right-6 bg-electric-blue text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-40 ".concat(d),whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:2,duration:.3},title:a,children:[m(),(0,s.jsx)("span",{className:"sr-only",children:a})]}):"inline"===t?(0,s.jsxs)("button",{onClick:u,className:"inline-flex items-center gap-2 text-electric-blue hover:text-indigo-700 font-medium transition-colors duration-200 ".concat(d),children:[m(),(0,s.jsx)("span",{children:a})]}):(0,s.jsxs)(l.P.button,{onClick:u,className:"inline-flex items-center gap-2 bg-electric-blue text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md hover:shadow-lg ".concat((()=>{switch(h){case"sm":return"px-3 py-2 text-sm";case"lg":return"px-6 py-4 text-lg";default:return"px-4 py-3 text-base"}})()," ").concat(d),whileHover:{scale:1.02},whileTap:{scale:.98},children:[m(),(0,s.jsx)("span",{children:a})]})},d={HeaderChat:()=>(0,s.jsx)(c,{variant:"inline",text:"Live Chat",icon:"message",context:"header",size:"sm"}),ContactChat:()=>(0,s.jsx)(c,{variant:"button",text:"Chat with Our Team",icon:"message",context:"contact",size:"lg",className:"w-full sm:w-auto"}),ServicesChat:()=>(0,s.jsx)(c,{variant:"button",text:"Ask About Pricing",icon:"help",context:"services",size:"md"}),FAQChat:()=>(0,s.jsx)(c,{variant:"inline",text:"Still have questions? Chat with us",icon:"help",context:"faq",size:"md"}),BlogChat:()=>(0,s.jsx)(c,{variant:"inline",text:"Discuss this article",icon:"message",context:"blog",size:"sm"}),FloatingChat:()=>(0,s.jsx)(c,{variant:"floating",text:"Chat with us",icon:"message",context:"floating"}),DemoChat:()=>(0,s.jsx)(c,{variant:"button",text:"Get Help with Demo",icon:"help",context:"demo",size:"md"})}}}]);
(()=>{var e={};e.id=103,e.ids=[103],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46721:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>l,POST:()=>u,PUT:()=>p});var o=t(96559),n=t(48088),i=t(37719),a=t(32190);async function u(e){try{let{email:r}=await e.json();if(!r||!r.includes("@"))return a.NextResponse.json({error:"Please provide a valid email address"},{status:400});let t=process.env.MAILCHIMP_API_KEY,s=process.env.MAILCHIMP_LIST_ID;if(!t||!s)return console.error("Missing Mailchimp configuration"),a.NextResponse.json({error:"Newsletter service is not configured. Please try again later."},{status:500});let o=t.split("-")[1];if(!o)return console.error("Invalid Mailchimp API key format"),a.NextResponse.json({error:"Newsletter service configuration error. Please try again later."},{status:500});let n={email_address:r.toLowerCase(),status:"subscribed",merge_fields:{SOURCE:"Mobilify Website"}},i=`https://${o}.api.mailchimp.com/3.0/lists/${s}/members`,u=await fetch(i,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify(n)}),l=await u.json();if(u.ok)return a.NextResponse.json({message:"Successfully subscribed to newsletter",email:r.toLowerCase()},{status:200});if("Member Exists"===l.title)return a.NextResponse.json({error:"This email is already subscribed to our newsletter."},{status:400});if("Invalid Resource"===l.title)return a.NextResponse.json({error:"Please provide a valid email address."},{status:400});return console.error("Mailchimp API error:",l),a.NextResponse.json({error:"Unable to subscribe at this time. Please try again later."},{status:500})}catch(e){return console.error("Newsletter subscription error:",e),a.NextResponse.json({error:"An unexpected error occurred. Please try again later."},{status:500})}}async function l(){return a.NextResponse.json({error:"Method not allowed"},{status:405})}async function p(){return a.NextResponse.json({error:"Method not allowed"},{status:405})}async function c(){return a.NextResponse.json({error:"Method not allowed"},{status:405})}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/newsletter/route",pathname:"/api/newsletter",filename:"route",bundlePath:"app/api/newsletter/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:f}=d;function w(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(46721));module.exports=s})();
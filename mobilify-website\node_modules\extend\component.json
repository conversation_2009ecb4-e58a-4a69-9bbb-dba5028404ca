{"name": "extend", "author": "<PERSON> <<EMAIL>> (http://www.justmoon.net)", "version": "3.0.0", "description": "Port of jQuery.extend for node.js and the browser.", "scripts": ["index.js"], "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "~3.0.0", "covert": "~0.4.0", "jscs": "~1.6.2"}}
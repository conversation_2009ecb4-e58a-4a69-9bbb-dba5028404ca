{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Logo = ({ className = \"\" }: { className?: string }) => {\n  return (\n    <div className={`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAA0B;IACtD,qBACE,8OAAC;QAAI,WAAW,CAAC,yEAAyE,EAAE,WAAW;kBACrG,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ChatTrigger.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle, HelpCircle, Phone } from 'lucide-react';\nimport { motion } from 'framer-motion';\nimport { crispUtils } from './CrispChat';\n\ninterface ChatTriggerProps {\n  variant?: 'button' | 'floating' | 'inline';\n  text?: string;\n  icon?: 'message' | 'help' | 'phone';\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  context?: string; // For analytics and session data\n}\n\nconst ChatTrigger: React.FC<ChatTriggerProps> = ({\n  variant = 'button',\n  text = 'Chat with us',\n  icon = 'message',\n  className = '',\n  size = 'md',\n  context = 'general'\n}) => {\n  const handleChatOpen = () => {\n    // Set context data for better support\n    crispUtils.setSessionData({\n      trigger_context: context,\n      trigger_page: window.location.pathname,\n      trigger_time: new Date().toISOString()\n    });\n\n    // Open the chat\n    crispUtils.openChat();\n\n    // Track the interaction\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_trigger_clicked', {\n        event_category: 'engagement',\n        event_label: context,\n        custom_parameter_1: variant\n      });\n    }\n  };\n\n  const getIcon = () => {\n    const iconProps = {\n      className: size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'\n    };\n\n    switch (icon) {\n      case 'help':\n        return <HelpCircle {...iconProps} />;\n      case 'phone':\n        return <Phone {...iconProps} />;\n      default:\n        return <MessageCircle {...iconProps} />;\n    }\n  };\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'px-3 py-2 text-sm';\n      case 'lg':\n        return 'px-6 py-4 text-lg';\n      default:\n        return 'px-4 py-3 text-base';\n    }\n  };\n\n  if (variant === 'floating') {\n    return (\n      <motion.button\n        onClick={handleChatOpen}\n        className={`fixed bottom-6 right-6 bg-electric-blue text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-40 ${className}`}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        initial={{ opacity: 0, scale: 0 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ delay: 2, duration: 0.3 }}\n        title={text}\n      >\n        {getIcon()}\n        <span className=\"sr-only\">{text}</span>\n      </motion.button>\n    );\n  }\n\n  if (variant === 'inline') {\n    return (\n      <button\n        onClick={handleChatOpen}\n        className={`inline-flex items-center gap-2 text-electric-blue hover:text-indigo-700 font-medium transition-colors duration-200 ${className}`}\n      >\n        {getIcon()}\n        <span>{text}</span>\n      </button>\n    );\n  }\n\n  // Default button variant\n  return (\n    <motion.button\n      onClick={handleChatOpen}\n      className={`inline-flex items-center gap-2 bg-electric-blue text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md hover:shadow-lg ${getSizeClasses()} ${className}`}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {getIcon()}\n      <span>{text}</span>\n    </motion.button>\n  );\n};\n\nexport default ChatTrigger;\n\n// Pre-configured chat triggers for common use cases\nexport const ChatTriggers = {\n  // For the header/navigation\n  HeaderChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Live Chat\"\n      icon=\"message\"\n      context=\"header\"\n      size=\"sm\"\n    />\n  ),\n\n  // For the contact section\n  ContactChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Chat with Our Team\"\n      icon=\"message\"\n      context=\"contact\"\n      size=\"lg\"\n      className=\"w-full sm:w-auto\"\n    />\n  ),\n\n  // For the services page\n  ServicesChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Ask About Pricing\"\n      icon=\"help\"\n      context=\"services\"\n      size=\"md\"\n    />\n  ),\n\n  // For the FAQ page\n  FAQChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Still have questions? Chat with us\"\n      icon=\"help\"\n      context=\"faq\"\n      size=\"md\"\n    />\n  ),\n\n  // For blog posts\n  BlogChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Discuss this article\"\n      icon=\"message\"\n      context=\"blog\"\n      size=\"sm\"\n    />\n  ),\n\n  // Floating action button (always visible)\n  FloatingChat: () => (\n    <ChatTrigger\n      variant=\"floating\"\n      text=\"Chat with us\"\n      icon=\"message\"\n      context=\"floating\"\n    />\n  ),\n\n  // For the demo section\n  DemoChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Get Help with Demo\"\n      icon=\"help\"\n      context=\"demo\"\n      size=\"md\"\n    />\n  )\n};\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AACA;AACA;AALA;;;;;AAgBA,MAAM,cAA0C,CAAC,EAC/C,UAAU,QAAQ,EAClB,OAAO,cAAc,EACrB,OAAO,SAAS,EAChB,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACpB;IACC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,+HAAA,CAAA,aAAU,CAAC,cAAc,CAAC;YACxB,iBAAiB;YACjB,cAAc,OAAO,QAAQ,CAAC,QAAQ;YACtC,cAAc,IAAI,OAAO,WAAW;QACtC;QAEA,gBAAgB;QAChB,+HAAA,CAAA,aAAU,CAAC,QAAQ;QAEnB,wBAAwB;QACxB,uCAA2D;;QAM3D;IACF;IAEA,MAAM,UAAU;QACd,MAAM,YAAY;YAChB,WAAW,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY;QACrE;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,8NAAA,CAAA,aAAU;oBAAE,GAAG,SAAS;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAE,GAAG,SAAS;;;;;;YAC7B;gBACE,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAE,GAAG,SAAS;;;;;;QACvC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,YAAY,YAAY;QAC1B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;YACZ,SAAS;YACT,WAAW,CAAC,+IAA+I,EAAE,WAAW;YACxK,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,OAAO;gBAAG,UAAU;YAAI;YACtC,OAAO;;gBAEN;8BACD,8OAAC;oBAAK,WAAU;8BAAW;;;;;;;;;;;;IAGjC;IAEA,IAAI,YAAY,UAAU;QACxB,qBACE,8OAAC;YACC,SAAS;YACT,WAAW,CAAC,mHAAmH,EAAE,WAAW;;gBAE3I;8BACD,8OAAC;8BAAM;;;;;;;;;;;;IAGb;IAEA,yBAAyB;IACzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAW,CAAC,2JAA2J,EAAE,iBAAiB,CAAC,EAAE,WAAW;QACxM,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;;YAEvB;0BACD,8OAAC;0BAAM;;;;;;;;;;;;AAGb;uCAEe;AAGR,MAAM,eAAe;IAC1B,4BAA4B;IAC5B,YAAY,kBACV,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,0BAA0B;IAC1B,aAAa,kBACX,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;YACL,WAAU;;;;;;IAId,wBAAwB;IACxB,cAAc,kBACZ,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,mBAAmB;IACnB,SAAS,kBACP,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,iBAAiB;IACjB,UAAU,kBACR,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,0CAA0C;IAC1C,cAAc,kBACZ,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;;;;;;IAIZ,uBAAuB;IACvB,UAAU,kBACR,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;AAGX", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleDarkModeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Sun, Moon } from 'lucide-react';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface SimpleDarkModeToggleProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst SimpleDarkModeToggle: React.FC<SimpleDarkModeToggleProps> = ({\n  className = '',\n  size = 'md'\n}) => {\n  const { theme, toggleTheme } = useTheme();\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'w-8 h-8 text-sm';\n      case 'lg':\n        return 'w-12 h-12 text-lg';\n      default:\n        return 'w-10 h-10 text-base';\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className={`${getSizeClasses()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${className}`}\n      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}\n    >\n      {theme === 'dark' ? (\n        <Sun className=\"w-5 h-5\" />\n      ) : (\n        <Moon className=\"w-5 h-5\" />\n      )}\n    </button>\n  );\n};\n\nexport default SimpleDarkModeToggle;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAJA;;;;AAWA,MAAM,uBAA4D,CAAC,EACjE,YAAY,EAAE,EACd,OAAO,IAAI,EACZ;IACC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEtC,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,GAAG,iBAAiB,gLAAgL,EAAE,WAAW;QAC5N,cAAY,CAAC,UAAU,EAAE,UAAU,SAAS,UAAU,OAAO,KAAK,CAAC;kBAElE,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;iCAEf,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\nimport Logo from './Logo';\nimport NoSSR from './NoSSR';\nimport { ChatTriggers } from './ChatTrigger';\nimport SimpleDarkModeToggle from './SimpleDarkModeToggle';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMenuOpen(false);\n  };\n\n  const navItems = [\n    { label: 'Services', href: '#services-overview' },\n    { label: 'How It Works', href: '#process' },\n    { label: 'About Us', href: '#about' },\n  ];\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300\">\n      <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 w-full\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n            <span className=\"ml-2 text-xl font-bold text-dark-charcoal dark:text-white\">Mobilify</span>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <button\n                key={item.label}\n                onClick={() => scrollToSection(item.href.substring(1))}\n                className=\"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200\"\n              >\n                {item.label}\n              </button>\n            ))}\n            <ChatTriggers.HeaderChat />\n            <NoSSR>\n              <SimpleDarkModeToggle size=\"sm\" className=\"mr-4\" />\n            </NoSSR>\n            <button\n              onClick={() => scrollToSection('contact')}\n              className=\"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200\"\n            >\n              Get a Quote\n            </button>\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100\"\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <NoSSR>\n        {isMenuOpen && (\n          <div className=\"md:hidden fixed inset-0 top-16 bg-white z-40\">\n            <div className=\"px-4 py-6 space-y-4\">\n              {navItems.map((item) => (\n                <button\n                  key={item.label}\n                  onClick={() => scrollToSection(item.href.substring(1))}\n                  className=\"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2\"\n                >\n                  {item.label}\n                </button>\n              ))}\n              <button\n                onClick={() => scrollToSection('contact')}\n                className=\"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6\"\n              >\n                Get a Quote\n              </button>\n            </div>\n          </div>\n        )}\n      </NoSSR>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,cAAc;IAChB;IAEA,MAAM,WAAW;QACf;YAAE,OAAO;YAAY,MAAM;QAAqB;QAChD;YAAE,OAAO;YAAgB,MAAM;QAAW;QAC1C;YAAE,OAAO;YAAY,MAAM;QAAS;KACrC;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,UAAI;;;;;8CACL,8OAAC;oCAAK,WAAU;8CAA4D;;;;;;;;;;;;sCAI9E,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;wCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI,CAAC,SAAS,CAAC;wCACnD,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,KAAK;;;;;8CAOnB,8OAAC,iIAAA,CAAA,eAAY,CAAC,UAAU;;;;;8CACxB,8OAAC,2HAAA,CAAA,UAAK;8CACJ,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAK,WAAU;;;;;;;;;;;8CAE5C,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMlD,8OAAC,2HAAA,CAAA,UAAK;0BACH,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI,CAAC,SAAS,CAAC;oCACnD,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,KAAK;;;;;0CAOnB,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/NewsletterSignup.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Mail, CheckCircle, AlertCircle } from 'lucide-react';\n\ninterface NewsletterSignupProps {\n  variant?: 'footer' | 'section';\n  className?: string;\n}\n\nconst NewsletterSignup: React.FC<NewsletterSignupProps> = ({ \n  variant = 'footer', \n  className = '' \n}) => {\n  const [email, setEmail] = useState('');\n  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  const [message, setMessage] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!email.trim()) {\n      setStatus('error');\n      setMessage('Please enter a valid email address');\n      return;\n    }\n\n    setStatus('loading');\n    \n    try {\n      const response = await fetch('/api/newsletter', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setStatus('success');\n        setMessage('Thanks for subscribing! Check your email for confirmation.');\n        setEmail('');\n        \n        // Track newsletter signup event\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'newsletter_signup', {\n            event_category: 'engagement',\n            event_label: variant\n          });\n        }\n      } else {\n        setStatus('error');\n        setMessage(data.error || 'Something went wrong. Please try again.');\n      }\n    } catch (error) {\n      setStatus('error');\n      setMessage('Network error. Please check your connection and try again.');\n    }\n\n    // Reset status after 5 seconds\n    setTimeout(() => {\n      setStatus('idle');\n      setMessage('');\n    }, 5000);\n  };\n\n  if (variant === 'footer') {\n    return (\n      <div className={`space-y-4 ${className}`}>\n        <h3 className=\"text-lg font-semibold text-dark-charcoal\">\n          Stay Updated\n        </h3>\n        <p className=\"text-gray-600 text-sm\">\n          Get the latest mobile app development tips and Mobilify updates.\n        </p>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-3\">\n          <div className=\"flex flex-col sm:flex-row gap-2\">\n            <input\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-transparent text-sm\"\n              disabled={status === 'loading'}\n            />\n            <button\n              type=\"submit\"\n              disabled={status === 'loading' || !email.trim()}\n              className=\"bg-electric-blue text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 text-sm whitespace-nowrap\"\n            >\n              {status === 'loading' ? 'Subscribing...' : 'Subscribe'}\n            </button>\n          </div>\n          \n          {message && (\n            <motion.div\n              initial={{ opacity: 0, y: -10 }}\n              animate={{ opacity: 1, y: 0 }}\n              className={`flex items-center gap-2 text-sm ${\n                status === 'success' ? 'text-green-600' : 'text-red-600'\n              }`}\n            >\n              {status === 'success' ? (\n                <CheckCircle className=\"w-4 h-4\" />\n              ) : (\n                <AlertCircle className=\"w-4 h-4\" />\n              )}\n              {message}\n            </motion.div>\n          )}\n        </form>\n      </div>\n    );\n  }\n\n  // Section variant\n  return (\n    <section className={`py-16 bg-gradient-to-r from-electric-blue to-indigo-600 ${className}`}>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"space-y-6\"\n        >\n          <div className=\"flex justify-center\">\n            <div className=\"bg-white/20 rounded-full p-3\">\n              <Mail className=\"w-8 h-8 text-white\" />\n            </div>\n          </div>\n          \n          <h2 className=\"text-3xl sm:text-4xl font-bold text-white\">\n            Get Mobile App Insights\n          </h2>\n          \n          <p className=\"text-xl text-blue-100 max-w-2xl mx-auto\">\n            Join 500+ entrepreneurs getting weekly tips on mobile app development, \n            industry trends, and exclusive Mobilify updates.\n          </p>\n          \n          <form onSubmit={handleSubmit} className=\"max-w-md mx-auto\">\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                placeholder=\"Enter your email address\"\n                className=\"flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-electric-blue text-dark-charcoal\"\n                disabled={status === 'loading'}\n              />\n              <button\n                type=\"submit\"\n                disabled={status === 'loading' || !email.trim()}\n                className=\"bg-white text-electric-blue px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 whitespace-nowrap\"\n              >\n                {status === 'loading' ? 'Subscribing...' : 'Get Updates'}\n              </button>\n            </div>\n            \n            {message && (\n              <motion.div\n                initial={{ opacity: 0, y: -10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className={`mt-4 flex items-center justify-center gap-2 ${\n                  status === 'success' ? 'text-green-100' : 'text-red-100'\n                }`}\n              >\n                {status === 'success' ? (\n                  <CheckCircle className=\"w-5 h-5\" />\n                ) : (\n                  <AlertCircle className=\"w-5 h-5\" />\n                )}\n                {message}\n              </motion.div>\n            )}\n          </form>\n          \n          <p className=\"text-blue-200 text-sm\">\n            No spam, ever. Unsubscribe with one click.\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default NewsletterSignup;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAWA,MAAM,mBAAoD,CAAC,EACzD,UAAU,QAAQ,EAClB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,UAAU;YACV,WAAW;YACX;QACF;QAEA,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;gBACV,WAAW;gBACX,SAAS;gBAET,gCAAgC;gBAChC,uCAA2D;;gBAK3D;YACF,OAAO;gBACL,UAAU;gBACV,WAAW,KAAK,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,UAAU;YACV,WAAW;QACb;QAEA,+BAA+B;QAC/B,WAAW;YACT,UAAU;YACV,WAAW;QACb,GAAG;IACL;IAEA,IAAI,YAAY,UAAU;QACxB,qBACE,8OAAC;YAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;8BACtC,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAGzD,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;8BAIrC,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;oCACV,UAAU,WAAW;;;;;;8CAEvB,8OAAC;oCACC,MAAK;oCACL,UAAU,WAAW,aAAa,CAAC,MAAM,IAAI;oCAC7C,WAAU;8CAET,WAAW,YAAY,mBAAmB;;;;;;;;;;;;wBAI9C,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAW,CAAC,gCAAgC,EAC1C,WAAW,YAAY,mBAAmB,gBAC1C;;gCAED,WAAW,0BACV,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAExB;;;;;;;;;;;;;;;;;;;IAMb;IAEA,kBAAkB;IAClB,qBACE,8OAAC;QAAQ,WAAW,CAAC,wDAAwD,EAAE,WAAW;kBACxF,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAIpB,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAI1D,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAKvD,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,WAAU;wCACV,UAAU,WAAW;;;;;;kDAEvB,8OAAC;wCACC,MAAK;wCACL,UAAU,WAAW,aAAa,CAAC,MAAM,IAAI;wCAC7C,WAAU;kDAET,WAAW,YAAY,mBAAmB;;;;;;;;;;;;4BAI9C,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAW,CAAC,4CAA4C,EACtD,WAAW,YAAY,mBAAmB,gBAC1C;;oCAED,WAAW,0BACV,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAExB;;;;;;;;;;;;;kCAKP,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C;uCAEe", "debugId": null}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Logo from './Logo';\nimport NewsletterSignup from './NewsletterSignup';\nimport SimpleDarkModeToggle from './SimpleDarkModeToggle';\nimport NoSSR from './NoSSR';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 dark:bg-gray-950 text-white py-12 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center mb-4\">\n              <Logo />\n              <span className=\"ml-2 text-xl font-bold\">Mobilify</span>\n            </div>\n            <p className=\"text-gray-400 text-sm leading-relaxed\">\n              Transforming ideas into mobile reality. We help entrepreneurs and businesses\n              create beautiful, high-performance mobile apps without the traditional complexity and cost.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <a href=\"/#demo\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  See Demo\n                </a>\n              </li>\n              <li>\n                <a href=\"/services\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  Services & Pricing\n                </a>\n              </li>\n              <li>\n                <a href=\"/about\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  About Us\n                </a>\n              </li>\n              <li>\n                <a href=\"/#contact\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                  Contact\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Newsletter Signup */}\n          <div className=\"lg:col-span-1\">\n            <NewsletterSignup variant=\"footer\" />\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 Mobilify. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n              <NoSSR>\n                <SimpleDarkModeToggle size=\"sm\" />\n              </NoSSR>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,UAAI;;;;;sDACL,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAOvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAgE;;;;;;;;;;;sDAI7F,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAgE;;;;;;;;;;;sDAI7F,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;;;;;;;;;;;;;sCAQpG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sIAAA,CAAA,UAAgB;gCAAC,SAAQ;;;;;;;;;;;;;;;;;8BAK9B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,UAAK;kDACJ,cAAA,8OAAC,0IAAA,CAAA,UAAoB;4CAAC,MAAK;;;;;;;;;;;kDAE7B,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAwE;;;;;;kDAG9F,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5G;uCAEe", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/PortableText.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { urlFor } from '../lib/sanity';\n\n// Types for Portable Text blocks\ninterface PortableTextBlock {\n  _type: string;\n  _key: string;\n  children?: PortableTextSpan[];\n  style?: string;\n  level?: number;\n  listItem?: string;\n  markDefs?: MarkDef[];\n}\n\ninterface PortableTextSpan {\n  _type: 'span';\n  _key: string;\n  text: string;\n  marks?: string[];\n}\n\ninterface MarkDef {\n  _type: string;\n  _key: string;\n  href?: string;\n}\n\ninterface ImageBlock {\n  _type: 'image';\n  _key: string;\n  asset: {\n    _ref: string;\n  };\n  alt?: string;\n  caption?: string;\n}\n\ntype PortableTextContent = PortableTextBlock | ImageBlock;\n\ninterface PortableTextProps {\n  content: PortableTextContent[];\n  className?: string;\n}\n\nconst PortableText: React.FC<PortableTextProps> = ({ content, className = '' }) => {\n  const renderBlock = (block: PortableTextContent, index: number) => {\n    if (block._type === 'image') {\n      const imageBlock = block as ImageBlock;\n      return (\n        <div key={block._key || index} className=\"my-8\">\n          <div className=\"relative w-full h-64 md:h-96 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center\">\n            <span className=\"text-gray-500 dark:text-gray-400\">\n              {imageBlock.alt || 'Blog image'}\n            </span>\n          </div>\n          {imageBlock.caption && (\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 text-center mt-2 italic\">\n              {imageBlock.caption}\n            </p>\n          )}\n        </div>\n      );\n    }\n\n    const textBlock = block as PortableTextBlock;\n    \n    if (!textBlock.children) return null;\n\n    const renderSpan = (span: PortableTextSpan, spanIndex: number) => {\n      let text = span.text;\n      \n      if (!span.marks || span.marks.length === 0) {\n        return text;\n      }\n\n      let element = <span key={spanIndex}>{text}</span>;\n\n      span.marks.forEach((mark) => {\n        if (mark === 'strong') {\n          element = <strong key={`${spanIndex}-strong`} className=\"font-semibold\">{element}</strong>;\n        } else if (mark === 'em') {\n          element = <em key={`${spanIndex}-em`} className=\"italic\">{element}</em>;\n        } else if (mark === 'code') {\n          element = <code key={`${spanIndex}-code`} className=\"bg-gray-100 px-1 py-0.5 rounded text-sm font-mono\">{element}</code>;\n        } else {\n          // Handle link marks\n          const linkMark = textBlock.markDefs?.find(def => def._key === mark);\n          if (linkMark && linkMark._type === 'link' && linkMark.href) {\n            const isExternal = linkMark.href.startsWith('http');\n            if (isExternal) {\n              element = (\n                <a\n                  key={`${spanIndex}-link`}\n                  href={linkMark.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-electric-blue hover:underline\"\n                >\n                  {element}\n                </a>\n              );\n            } else {\n              element = (\n                <Link\n                  key={`${spanIndex}-link`}\n                  href={linkMark.href}\n                  className=\"text-electric-blue hover:underline\"\n                >\n                  {element}\n                </Link>\n              );\n            }\n          }\n        }\n      });\n\n      return element;\n    };\n\n    const children = textBlock.children.map(renderSpan);\n\n    switch (textBlock.style) {\n      case 'h1':\n        return (\n          <h1 key={textBlock._key || index} className=\"text-3xl md:text-4xl font-bold text-dark-charcoal mb-6 mt-8\">\n            {children}\n          </h1>\n        );\n      case 'h2':\n        return (\n          <h2 key={textBlock._key || index} className=\"text-2xl md:text-3xl font-bold text-dark-charcoal mb-4 mt-8\">\n            {children}\n          </h2>\n        );\n      case 'h3':\n        return (\n          <h3 key={textBlock._key || index} className=\"text-xl md:text-2xl font-semibold text-dark-charcoal mb-3 mt-6\">\n            {children}\n          </h3>\n        );\n      case 'h4':\n        return (\n          <h4 key={textBlock._key || index} className=\"text-lg md:text-xl font-semibold text-dark-charcoal mb-2 mt-4\">\n            {children}\n          </h4>\n        );\n      case 'blockquote':\n        return (\n          <blockquote key={textBlock._key || index} className=\"border-l-4 border-electric-blue pl-4 py-2 my-6 italic text-gray-700 bg-gray-50 rounded-r\">\n            {children}\n          </blockquote>\n        );\n      default:\n        if (textBlock.listItem === 'bullet') {\n          return (\n            <li key={textBlock._key || index} className=\"mb-2\">\n              {children}\n            </li>\n          );\n        }\n        if (textBlock.listItem === 'number') {\n          return (\n            <li key={textBlock._key || index} className=\"mb-2\">\n              {children}\n            </li>\n          );\n        }\n        return (\n          <p key={textBlock._key || index} className=\"mb-4 leading-relaxed text-gray-700\">\n            {children}\n          </p>\n        );\n    }\n  };\n\n  // Group list items\n  const groupedContent: (PortableTextContent | PortableTextContent[])[] = [];\n  let currentList: PortableTextContent[] = [];\n  let currentListType: string | null = null;\n\n  content.forEach((block) => {\n    if (block._type === 'block' && (block as PortableTextBlock).listItem) {\n      const listType = (block as PortableTextBlock).listItem;\n      if (listType === currentListType) {\n        currentList.push(block);\n      } else {\n        if (currentList.length > 0) {\n          groupedContent.push([...currentList]);\n        }\n        currentList = [block];\n        currentListType = listType;\n      }\n    } else {\n      if (currentList.length > 0) {\n        groupedContent.push([...currentList]);\n        currentList = [];\n        currentListType = null;\n      }\n      groupedContent.push(block);\n    }\n  });\n\n  if (currentList.length > 0) {\n    groupedContent.push([...currentList]);\n  }\n\n  return (\n    <div className={`prose prose-lg max-w-none ${className}`}>\n      {groupedContent.map((item, index) => {\n        if (Array.isArray(item)) {\n          const listType = (item[0] as PortableTextBlock).listItem;\n          const ListComponent = listType === 'bullet' ? 'ul' : 'ol';\n          return (\n            <ListComponent key={index} className={listType === 'bullet' ? 'list-disc pl-6 mb-4' : 'list-decimal pl-6 mb-4'}>\n              {item.map((listItem, listIndex) => renderBlock(listItem, listIndex))}\n            </ListComponent>\n          );\n        }\n        return renderBlock(item, index);\n      })}\n    </div>\n  );\n};\n\nexport default PortableText;\n"], "names": [], "mappings": ";;;;AACA;;;AA6CA,MAAM,eAA4C,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;IAC5E,MAAM,cAAc,CAAC,OAA4B;QAC/C,IAAI,MAAM,KAAK,KAAK,SAAS;YAC3B,MAAM,aAAa;YACnB,qBACE,8OAAC;gBAA8B,WAAU;;kCACvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACb,WAAW,GAAG,IAAI;;;;;;;;;;;oBAGtB,WAAW,OAAO,kBACjB,8OAAC;wBAAE,WAAU;kCACV,WAAW,OAAO;;;;;;;eARf,MAAM,IAAI,IAAI;;;;;QAa5B;QAEA,MAAM,YAAY;QAElB,IAAI,CAAC,UAAU,QAAQ,EAAE,OAAO;QAEhC,MAAM,aAAa,CAAC,MAAwB;YAC1C,IAAI,OAAO,KAAK,IAAI;YAEpB,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC1C,OAAO;YACT;YAEA,IAAI,wBAAU,8OAAC;0BAAsB;eAAZ;;;;;YAEzB,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;gBAClB,IAAI,SAAS,UAAU;oBACrB,wBAAU,8OAAC;wBAAmC,WAAU;kCAAiB;uBAAlD,GAAG,UAAU,OAAO,CAAC;;;;;gBAC9C,OAAO,IAAI,SAAS,MAAM;oBACxB,wBAAU,8OAAC;wBAA2B,WAAU;kCAAU;uBAAvC,GAAG,UAAU,GAAG,CAAC;;;;;gBACtC,OAAO,IAAI,SAAS,QAAQ;oBAC1B,wBAAU,8OAAC;wBAA+B,WAAU;kCAAqD;uBAApF,GAAG,UAAU,KAAK,CAAC;;;;;gBAC1C,OAAO;oBACL,oBAAoB;oBACpB,MAAM,WAAW,UAAU,QAAQ,EAAE,KAAK,CAAA,MAAO,IAAI,IAAI,KAAK;oBAC9D,IAAI,YAAY,SAAS,KAAK,KAAK,UAAU,SAAS,IAAI,EAAE;wBAC1D,MAAM,aAAa,SAAS,IAAI,CAAC,UAAU,CAAC;wBAC5C,IAAI,YAAY;4BACd,wBACE,8OAAC;gCAEC,MAAM,SAAS,IAAI;gCACnB,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAET;+BANI,GAAG,UAAU,KAAK,CAAC;;;;;wBAS9B,OAAO;4BACL,wBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,SAAS,IAAI;gCACnB,WAAU;0CAET;+BAJI,GAAG,UAAU,KAAK,CAAC;;;;;wBAO9B;oBACF;gBACF;YACF;YAEA,OAAO;QACT;QAEA,MAAM,WAAW,UAAU,QAAQ,CAAC,GAAG,CAAC;QAExC,OAAQ,UAAU,KAAK;YACrB,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAiC,WAAU;8BACzC;mBADM,UAAU,IAAI,IAAI;;;;;YAI/B,KAAK;gBACH,qBACE,8OAAC;oBAAyC,WAAU;8BACjD;mBADc,UAAU,IAAI,IAAI;;;;;YAIvC;gBACE,IAAI,UAAU,QAAQ,KAAK,UAAU;oBACnC,qBACE,8OAAC;wBAAiC,WAAU;kCACzC;uBADM,UAAU,IAAI,IAAI;;;;;gBAI/B;gBACA,IAAI,UAAU,QAAQ,KAAK,UAAU;oBACnC,qBACE,8OAAC;wBAAiC,WAAU;kCACzC;uBADM,UAAU,IAAI,IAAI;;;;;gBAI/B;gBACA,qBACE,8OAAC;oBAAgC,WAAU;8BACxC;mBADK,UAAU,IAAI,IAAI;;;;;QAIhC;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAkE,EAAE;IAC1E,IAAI,cAAqC,EAAE;IAC3C,IAAI,kBAAiC;IAErC,QAAQ,OAAO,CAAC,CAAC;QACf,IAAI,MAAM,KAAK,KAAK,WAAW,AAAC,MAA4B,QAAQ,EAAE;YACpE,MAAM,WAAW,AAAC,MAA4B,QAAQ;YACtD,IAAI,aAAa,iBAAiB;gBAChC,YAAY,IAAI,CAAC;YACnB,OAAO;gBACL,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,eAAe,IAAI,CAAC;2BAAI;qBAAY;gBACtC;gBACA,cAAc;oBAAC;iBAAM;gBACrB,kBAAkB;YACpB;QACF,OAAO;YACL,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,eAAe,IAAI,CAAC;uBAAI;iBAAY;gBACpC,cAAc,EAAE;gBAChB,kBAAkB;YACpB;YACA,eAAe,IAAI,CAAC;QACtB;IACF;IAEA,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,eAAe,IAAI,CAAC;eAAI;SAAY;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0BAA0B,EAAE,WAAW;kBACrD,eAAe,GAAG,CAAC,CAAC,MAAM;YACzB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,MAAM,WAAW,AAAC,IAAI,CAAC,EAAE,CAAuB,QAAQ;gBACxD,MAAM,gBAAgB,aAAa,WAAW,OAAO;gBACrD,qBACE,8OAAC;oBAA0B,WAAW,aAAa,WAAW,wBAAwB;8BACnF,KAAK,GAAG,CAAC,CAAC,UAAU,YAAc,YAAY,UAAU;mBADvC;;;;;YAIxB;YACA,OAAO,YAAY,MAAM;QAC3B;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 1379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/faq/FAQClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport Link from 'next/link';\nimport { Search, ChevronDown, ChevronUp, ExternalLink } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Header from '../../components/Header';\nimport Footer from '../../components/Footer';\nimport PortableText from '../../components/PortableText';\nimport { FAQItem } from '../../lib/sanity';\n\ninterface FAQClientProps {\n  faqItems: FAQItem[];\n}\n\nexport default function FAQClient({ faqItems = [] }: FAQClientProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [openItems, setOpenItems] = useState<Set<string>>(new Set());\n  const [selectedTopic, setSelectedTopic] = useState<string>('all');\n\n  // Get unique topics\n  const topics = useMemo(() => {\n    const topicSet = new Set(faqItems.map(item => item.topic.title));\n    return Array.from(topicSet).sort();\n  }, [faqItems]);\n\n  // Filter FAQ items based on search and topic\n  const filteredItems = useMemo(() => {\n    return faqItems.filter(item => {\n      const matchesSearch = searchTerm === '' || \n        item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.answer.some(block => \n          block._type === 'block' && \n          block.children?.some(child => \n            child.text?.toLowerCase().includes(searchTerm.toLowerCase())\n          )\n        );\n      \n      const matchesTopic = selectedTopic === 'all' || item.topic.title === selectedTopic;\n      \n      return matchesSearch && matchesTopic;\n    });\n  }, [faqItems, searchTerm, selectedTopic]);\n\n  // Group items by topic\n  const groupedItems = useMemo(() => {\n    const groups: { [key: string]: FAQItem[] } = {};\n    filteredItems.forEach(item => {\n      const topicTitle = item.topic.title;\n      if (!groups[topicTitle]) {\n        groups[topicTitle] = [];\n      }\n      groups[topicTitle].push(item);\n    });\n    return groups;\n  }, [filteredItems]);\n\n  const toggleItem = (itemId: string) => {\n    const newOpenItems = new Set(openItems);\n    if (newOpenItems.has(itemId)) {\n      newOpenItems.delete(itemId);\n    } else {\n      newOpenItems.add(itemId);\n    }\n    setOpenItems(newOpenItems);\n  };\n\n  // Generate JSON-LD structured data\n  const jsonLd = {\n    '@context': 'https://schema.org',\n    '@type': 'FAQPage',\n    mainEntity: faqItems.map(item => ({\n      '@type': 'Question',\n      name: item.question,\n      acceptedAnswer: {\n        '@type': 'Answer',\n        text: item.answer.map(block => \n          block._type === 'block' && block.children \n            ? block.children.map(child => child.text).join(' ')\n            : ''\n        ).join(' '),\n      },\n    })),\n  };\n\n  return (\n    <>\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}\n      />\n      \n      <div className=\"min-h-screen w-full overflow-x-hidden\">\n        <Header />\n        \n        <main className=\"pt-16\">\n          {/* Hero Section */}\n          <section className=\"py-16 bg-gradient-to-r from-electric-blue to-indigo-600\">\n            <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n              <h1 className=\"text-4xl md:text-5xl font-bold text-white mb-4\">\n                Frequently Asked Questions\n              </h1>\n              <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n                Find answers to common questions about mobile app development, our process, pricing, and more.\n              </p>\n            </div>\n          </section>\n\n          {/* Search and Filter */}\n          <section className=\"py-8 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 transition-colors duration-300\">\n            <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {/* Search */}\n              <div className=\"relative mb-6\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search FAQ...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-transparent\"\n                />\n              </div>\n\n              {/* Topic Filter */}\n              <div className=\"flex flex-wrap gap-2\">\n                <button\n                  onClick={() => setSelectedTopic('all')}\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${\n                    selectedTopic === 'all'\n                      ? 'bg-electric-blue text-white'\n                      : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  All Topics\n                </button>\n                {topics.map((topic) => (\n                  <button\n                    key={topic}\n                    onClick={() => setSelectedTopic(topic)}\n                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${\n                      selectedTopic === topic\n                        ? 'bg-electric-blue text-white'\n                        : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'\n                    }`}\n                  >\n                    {topic}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </section>\n\n          {/* FAQ Items */}\n          <section className=\"py-16 bg-white dark:bg-gray-900 transition-colors duration-300\">\n            <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {Object.keys(groupedItems).length === 0 ? (\n                <div className=\"text-center py-16\">\n                  <h2 className=\"text-2xl font-bold text-dark-charcoal dark:text-white mb-4\">\n                    No questions found\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-300 mb-8\">\n                    Try adjusting your search terms or topic filter.\n                  </p>\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setSelectedTopic('all');\n                    }}\n                    className=\"bg-electric-blue text-white px-6 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-200\"\n                  >\n                    Clear Filters\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-8\">\n                  {Object.entries(groupedItems).map(([topicTitle, items]) => (\n                    <div key={topicTitle}>\n                      <h2 className=\"text-2xl font-bold text-dark-charcoal dark:text-white mb-6 pb-2 border-b-2 border-electric-blue\">\n                        {topicTitle}\n                      </h2>\n                      \n                      <div className=\"space-y-4\">\n                        {items.map((item) => (\n                          <motion.div\n                            key={item._id}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200\"\n                          >\n                            <button\n                              onClick={() => toggleItem(item._id)}\n                              className=\"w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200\"\n                            >\n                              <h3 className=\"text-lg font-semibold text-dark-charcoal dark:text-white pr-4\">\n                                {item.question}\n                              </h3>\n                              {openItems.has(item._id) ? (\n                                <ChevronUp className=\"w-5 h-5 text-electric-blue flex-shrink-0\" />\n                              ) : (\n                                <ChevronDown className=\"w-5 h-5 text-electric-blue flex-shrink-0\" />\n                              )}\n                            </button>\n                            \n                            <AnimatePresence>\n                              {openItems.has(item._id) && (\n                                <motion.div\n                                  initial={{ height: 0, opacity: 0 }}\n                                  animate={{ height: 'auto', opacity: 1 }}\n                                  exit={{ height: 0, opacity: 0 }}\n                                  transition={{ duration: 0.3 }}\n                                  className=\"overflow-hidden\"\n                                >\n                                  <div className=\"px-6 pb-6\">\n                                    <div className=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                                      <PortableText content={item.answer} className=\"text-gray-700 dark:text-gray-300\" />\n                                      \n                                      {/* Related Posts */}\n                                      {item.relatedPosts && item.relatedPosts.length > 0 && (\n                                        <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\n                                          <h4 className=\"text-sm font-semibold text-dark-charcoal dark:text-white mb-3\">\n                                            Related Articles:\n                                          </h4>\n                                          <div className=\"space-y-2\">\n                                            {item.relatedPosts.map((post) => (\n                                              <Link\n                                                key={post._id}\n                                                href={`/blog/${post.slug.current}`}\n                                                className=\"inline-flex items-center text-electric-blue hover:underline text-sm\"\n                                              >\n                                                {post.title}\n                                                <ExternalLink className=\"ml-1 w-3 h-3\" />\n                                              </Link>\n                                            ))}\n                                          </div>\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n                                </motion.div>\n                              )}\n                            </AnimatePresence>\n                          </motion.div>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </section>\n\n          {/* CTA Section */}\n          <section className=\"py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-300\">\n            <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n              <h2 className=\"text-3xl font-bold text-dark-charcoal dark:text-white mb-4\">\n                Still have questions?\n              </h2>\n              <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8\">\n                We're here to help! Get in touch with our team for personalized answers.\n              </p>\n              <Link\n                href=\"/#contact\"\n                className=\"inline-flex items-center bg-electric-blue text-white px-8 py-4 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl\"\n              >\n                Contact Us\n              </Link>\n            </div>\n          </section>\n        </main>\n\n        <Footer />\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAee,SAAS,UAAU,EAAE,WAAW,EAAE,EAAkB;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,oBAAoB;IACpB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrB,MAAM,WAAW,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,KAAK;QAC9D,OAAO,MAAM,IAAI,CAAC,UAAU,IAAI;IAClC,GAAG;QAAC;KAAS;IAEb,6CAA6C;IAC7C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAA;YACrB,MAAM,gBAAgB,eAAe,MACnC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA,QACf,MAAM,KAAK,KAAK,WAChB,MAAM,QAAQ,EAAE,KAAK,CAAA,QACnB,MAAM,IAAI,EAAE,cAAc,SAAS,WAAW,WAAW;YAI/D,MAAM,eAAe,kBAAkB,SAAS,KAAK,KAAK,CAAC,KAAK,KAAK;YAErE,OAAO,iBAAiB;QAC1B;IACF,GAAG;QAAC;QAAU;QAAY;KAAc;IAExC,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,MAAM,SAAuC,CAAC;QAC9C,cAAc,OAAO,CAAC,CAAA;YACpB,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK;YACnC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBACvB,MAAM,CAAC,WAAW,GAAG,EAAE;YACzB;YACA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;QAC1B;QACA,OAAO;IACT,GAAG;QAAC;KAAc;IAElB,MAAM,aAAa,CAAC;QAClB,MAAM,eAAe,IAAI,IAAI;QAC7B,IAAI,aAAa,GAAG,CAAC,SAAS;YAC5B,aAAa,MAAM,CAAC;QACtB,OAAO;YACL,aAAa,GAAG,CAAC;QACnB;QACA,aAAa;IACf;IAEA,mCAAmC;IACnC,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,YAAY,SAAS,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAChC,SAAS;gBACT,MAAM,KAAK,QAAQ;gBACnB,gBAAgB;oBACd,SAAS;oBACT,MAAM,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,QACpB,MAAM,KAAK,KAAK,WAAW,MAAM,QAAQ,GACrC,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,EAAE,IAAI,CAAC,OAC7C,IACJ,IAAI,CAAC;gBACT;YACF,CAAC;IACH;IAEA,qBACE;;0BACE,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBAAE,QAAQ,KAAK,SAAS,CAAC;gBAAQ;;;;;;0BAG5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,UAAM;;;;;kCAEP,8OAAC;wBAAK,WAAU;;0CAEd,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;sDAG/D,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;;;;;;0CAO3D,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAW,CAAC,0EAA0E,EACpF,kBAAkB,QACd,gCACA,0GACJ;8DACH;;;;;;gDAGA,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wDAEC,SAAS,IAAM,iBAAiB;wDAChC,WAAW,CAAC,0EAA0E,EACpF,kBAAkB,QACd,gCACA,0GACJ;kEAED;uDARI;;;;;;;;;;;;;;;;;;;;;;0CAgBf,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK,kBACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6D;;;;;;0DAG3E,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;0DAGrD,8OAAC;gDACC,SAAS;oDACP,cAAc;oDACd,iBAAiB;gDACnB;gDACA,WAAU;0DACX;;;;;;;;;;;6DAKH,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,YAAY,MAAM,iBACpD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX;;;;;;kEAGH,8OAAC;wDAAI,WAAU;kEACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,WAAU;;kFAEV,8OAAC;wEACC,SAAS,IAAM,WAAW,KAAK,GAAG;wEAClC,WAAU;;0FAEV,8OAAC;gFAAG,WAAU;0FACX,KAAK,QAAQ;;;;;;4EAEf,UAAU,GAAG,CAAC,KAAK,GAAG,kBACrB,8OAAC,gNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;qGAErB,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;;;;;;;kFAI3B,8OAAC,yLAAA,CAAA,kBAAe;kFACb,UAAU,GAAG,CAAC,KAAK,GAAG,mBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4EACT,SAAS;gFAAE,QAAQ;gFAAG,SAAS;4EAAE;4EACjC,SAAS;gFAAE,QAAQ;gFAAQ,SAAS;4EAAE;4EACtC,MAAM;gFAAE,QAAQ;gFAAG,SAAS;4EAAE;4EAC9B,YAAY;gFAAE,UAAU;4EAAI;4EAC5B,WAAU;sFAEV,cAAA,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,kIAAA,CAAA,UAAY;4FAAC,SAAS,KAAK,MAAM;4FAAE,WAAU;;;;;;wFAG7C,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,GAAG,mBAC/C,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGAAG,WAAU;8GAAgE;;;;;;8GAG9E,8OAAC;oGAAI,WAAU;8GACZ,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC,4JAAA,CAAA,UAAI;4GAEH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE;4GAClC,WAAU;;gHAET,KAAK,KAAK;8HACX,8OAAC,sNAAA,CAAA,eAAY;oHAAC,WAAU;;;;;;;2GALnB,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+DAzC5B,KAAK,GAAG;;;;;;;;;;;+CARX;;;;;;;;;;;;;;;;;;;;0CA4EpB,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6D;;;;;;sDAG3E,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;sDAG7D,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAOP,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;;AAIf", "debugId": null}}]}
# 🎨 Mobilify Design System

This document is the single source of truth for all design tokens, components, and usage guidelines for the Mobilify website. Adhering to this system ensures a consistent, high-quality, and scalable user experience.

## Color Palette

Our color palette is modern, accessible, and built around semantic tokens defined in `tailwind.config.js`.

| Token             | Hex       | Usage                                                              |
| ----------------- | --------- | ------------------------------------------------------------------ |
| `dark-charcoal`   | `#111827` | Primary text, dark backgrounds (in light mode)                     |
| `electric-blue`   | `#4f46e5` | Primary actions, links, accents, focus rings                       |
| `white`           | `#ffffff` | Primary background (light mode), primary text (in dark mode)       |
| `gray-50` to `900`| `...`     | Secondary text, borders, disabled states, subtle backgrounds       |
| `red-500`         | `...`     | Error states, destructive actions                                  |
| `green-500`       | `...`     | Success states, positive feedback                                  |

**Dark Mode:** In dark mode, text and background colors are inverted (e.g., `dark-charcoal` becomes `white` for text), and background shades are adjusted from the gray palette to maintain proper contrast.

## Typography

We use the "Inter" font family for its excellent readability on screens. The system is designed with a clear and responsive hierarchy.

### Heading Hierarchy

- **H1 (Page Titles):** `text-4xl md:text-5xl lg:text-6xl font-bold`
- **H2 (Section Titles):** `text-3xl md:text-4xl font-bold`
- **H3 (Subsection Titles):** `text-2xl md:text-3xl font-semibold`
- **H4 (Card Titles, FAQ Questions):** `text-xl font-semibold`

### Body Text

- **Lead/Hero Text:** `text-lg md:text-xl leading-relaxed`
- **Standard Body:** `text-base leading-relaxed`
- **Small/Caption Text:** `text-sm` (for metadata, captions, etc.)

### Interactive Text

- **Primary Buttons:** `font-semibold`
- **Secondary Buttons:** `font-medium`
- **Links:** `font-medium hover:underline`

## Spacing & Layout

A consistent spacing scale ensures a balanced and harmonious layout across all pages and components.

- **Section Spacing:** `py-16 md:py-20` for main sections, `py-8 md:py-12` for subsections.
- **Container Widths:** `max-w-7xl` for main content, `max-w-4xl` for text-heavy content, `max-w-2xl` for forms.
- **Grid & Flex Gaps:** `gap-8 md:gap-12` for main grids, `gap-4 md:gap-6` for lists, `gap-2` for inline elements.
- **Container Padding:** `px-4 sm:px-6 lg:px-8` for all main containers.

## Component Variants

Standardized component variants ensure a cohesive look and feel for all interactive elements.

### Buttons

- **Primary:** `bg-electric-blue text-white hover:opacity-90 focus:ring-2 focus:ring-electric-blue`
- **Secondary:** `border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white`
- **Ghost:** `text-electric-blue hover:bg-electric-blue hover:bg-opacity-10`

### Form Inputs

- **Base:** `border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-electric-blue`
- **Error State:** `border-red-500 focus:ring-red-500`
- **Success State:** `border-green-500 focus:ring-green-500`

### Cards

- **Base:** `bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700`
- **Hover:** `hover:shadow-md transition-shadow duration-200`
- **Interactive:** `hover:shadow-lg cursor-pointer`

## Responsive Design

The entire site is built with a mobile-first approach, ensuring a seamless experience on any device.

- **Mobile-First:** Styles are defined for mobile and enhanced for larger screens using breakpoints.
- **Breakpoints:** Standard Tailwind CSS breakpoints are used:
  - `sm`: 640px
  - `md`: 768px
  - `lg`: 1024px
  - `xl`: 1280px
- **Touch Targets:** All interactive elements on mobile have a minimum touch target size of 44x44 pixels to meet accessibility standards.
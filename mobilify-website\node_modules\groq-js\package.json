{"name": "groq-js", "version": "1.17.1", "keywords": ["sanity", "json", "query", "groq"], "repository": {"type": "git", "url": "git+https://github.com/sanity-io/groq-js.git"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "commonjs", "exports": {".": {"source": "./src/1.ts", "import": "./dist/index.mjs", "require": "./dist/index.js", "default": "./dist/index.js"}, "./1": {"source": "./src/1.ts", "import": "./dist/1.mjs", "require": "./dist/1.js", "default": "./dist/1.js"}, "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"1": ["./dist/1.d.ts"]}}, "files": ["dist", "src", "LICENSE", "API.md", "README.md"], "scripts": {"build": "pkg build --strict --check --clean", "prepublishOnly": "npm run build", "prettify": "prettier --write .", "prettify-check": "prettier --check .", "test": "tap --no-timeout --coverage-report=html --no-browser test/*.test.*", "test:generate": "./test/generate.sh"}, "browserslist": "extends @sanity/browserslist-config", "prettier": "@sanity/prettier-config", "tap": {"check-coverage": false, "node-arg": ["-r", "tsx/cjs"]}, "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@sanity/pkg-utils": "6.11.14", "@sanity/prettier-config": "^1.0.3", "@sanity/semantic-release-preset": "^5.0.0", "@types/debug": "^4.1.12", "@types/tap": "^15.0.12", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-sanity": "^7.1.3", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "ndjson": "^2.0.0", "prettier": "^3.4.2", "rimraf": "^5.0.0", "semantic-release": "^24.2.0", "semver": "^7.5.4", "tap": "^16.3.10", "tsx": "^4.19.2", "typescript": "5.7.2"}, "engines": {"node": ">= 14"}}
exports.id=777,exports.ids=[777],exports.modules={1135:()=>{},1188:(e,t,s)=>{"use strict";s.d(t,{DP:()=>n,ThemeProvider:()=>l});var o=s(687),r=s(3210);let i=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},l=({children:e})=>{let[t,s]=(0,r.useState)("light"),[n,l]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s(e||t),l(!0)},[]),(0,r.useEffect)(()=>{if(!n)return;let e=document.documentElement;"dark"===t?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",t)},[t,n]),(0,r.useEffect)(()=>{if(!n)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||s(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[n]),(0,o.jsx)(i.Provider,{value:{theme:t,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")},setTheme:e=>{s(e)}},children:(0,o.jsx)("div",{suppressHydrationWarning:!0,children:e})})}},4083:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var o=s(2907);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\CrispChat.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CrispChat.tsx","default");(0,o.registerClientReference)(function(){throw Error("Attempted to call crispUtils() from the server but crispUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CrispChat.tsx","crispUtils")},4089:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},4272:(e,t,s)=>{Promise.resolve().then(s.bind(s,4083)),Promise.resolve().then(s.bind(s,7023)),Promise.resolve().then(s.bind(s,7463)),Promise.resolve().then(s.bind(s,8462))},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var o=s(7413),r=s(5759),i=s.n(r);s(1135);var n=s(7023),l=s(4083),a=s(8462),m=s(7463);let c={title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"};function d({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:`${i().variable} font-sans antialiased text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-900 transition-colors duration-300`,suppressHydrationWarning:!0,children:(0,o.jsxs)(a.ThemeProvider,{children:[e,(0,o.jsxs)(m.default,{children:[(0,o.jsx)(n.default,{}),(0,o.jsx)(l.default,{})]})]})})})}},7009:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var o=s(687),r=s(3210);let i=({children:e,fallback:t=null})=>{let[s,i]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{i(!0)},[]),s)?(0,o.jsx)(o.Fragment,{children:e}):(0,o.jsx)(o.Fragment,{children:t})}},7023:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});let o=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\GoogleAnalytics.tsx","default")},7296:(e,t,s)=>{Promise.resolve().then(s.bind(s,8569)),Promise.resolve().then(s.bind(s,7689)),Promise.resolve().then(s.bind(s,7009)),Promise.resolve().then(s.bind(s,1188))},7463:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});let o=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\NoSSR.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx","default")},7641:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},7689:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var o=s(3210);let r=()=>((0,o.useEffect)(()=>{},[]),null)},8462:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>r});var o=s(2907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","useTheme");let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","ThemeProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useSystemTheme() from the server but useSystemTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","useSystemTheme"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getThemeClasses() from the server but getThemeClasses is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","getThemeClasses")},8569:(e,t,s)=>{"use strict";s.d(t,{default:()=>r,l:()=>i});var o=s(3210);let r=({websiteId:e})=>((0,o.useEffect)(()=>e||process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID?()=>{}:void console.warn("Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable."),[e]),null),i={openChat:()=>{},closeChat:()=>{},showChat:()=>{},hideChat:()=>{},setUser:e=>{},sendMessage:e=>{},setSessionData:e=>{},isChatAvailable:()=>!1,setSegments:e=>{}}}};
"use strict";exports.id=383,exports.ids=[383],exports.modules={1477:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=a(687),i=a(3210),r=a(6001),l=a(5336),n=a(3613),o=a(3931);let c=({variant:e="footer",className:t=""})=>{let[a,c]=(0,i.useState)(""),[d,x]=(0,i.useState)("idle"),[h,u]=(0,i.useState)(""),m=async e=>{if(e.preventDefault(),!a.trim()){x("error"),u("Please enter a valid email address");return}x("loading");try{let e=await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a})}),t=await e.json();e.ok?(x("success"),u("Thanks for subscribing! Check your email for confirmation."),c("")):(x("error"),u(t.error||"Something went wrong. Please try again."))}catch(e){x("error"),u("Network error. Please check your connection and try again.")}setTimeout(()=>{x("idle"),u("")},5e3)};return"footer"===e?(0,s.jsxs)("div",{className:`space-y-4 ${t}`,children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-dark-charcoal",children:"Stay Updated"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Get the latest mobile app development tips and Mobilify updates."}),(0,s.jsxs)("form",{onSubmit:m,className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,s.jsx)("input",{type:"email",value:a,onChange:e=>c(e.target.value),placeholder:"Enter your email",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-transparent text-sm",disabled:"loading"===d}),(0,s.jsx)("button",{type:"submit",disabled:"loading"===d||!a.trim(),className:"bg-electric-blue text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 text-sm whitespace-nowrap",children:"loading"===d?"Subscribing...":"Subscribe"})]}),h&&(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`flex items-center gap-2 text-sm ${"success"===d?"text-green-600":"text-red-600"}`,children:["success"===d?(0,s.jsx)(l.A,{className:"w-4 h-4"}):(0,s.jsx)(n.A,{className:"w-4 h-4"}),h]})]})]}):(0,s.jsx)("section",{className:`py-16 bg-gradient-to-r from-electric-blue to-indigo-600 ${t}`,children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"bg-white/20 rounded-full p-3",children:(0,s.jsx)(o.A,{className:"w-8 h-8 text-white"})})}),(0,s.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-white",children:"Get Mobile App Insights"}),(0,s.jsx)("p",{className:"text-xl text-blue-100 max-w-2xl mx-auto",children:"Join 500+ entrepreneurs getting weekly tips on mobile app development, industry trends, and exclusive Mobilify updates."}),(0,s.jsxs)("form",{onSubmit:m,className:"max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsx)("input",{type:"email",value:a,onChange:e=>c(e.target.value),placeholder:"Enter your email address",className:"flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-electric-blue text-dark-charcoal",disabled:"loading"===d}),(0,s.jsx)("button",{type:"submit",disabled:"loading"===d||!a.trim(),className:"bg-white text-electric-blue px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 whitespace-nowrap",children:"loading"===d?"Subscribing...":"Get Updates"})]}),h&&(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mt-4 flex items-center justify-center gap-2 ${"success"===d?"text-green-100":"text-red-100"}`,children:["success"===d?(0,s.jsx)(l.A,{className:"w-5 h-5"}):(0,s.jsx)(n.A,{className:"w-5 h-5"}),h]})]}),(0,s.jsx)("p",{className:"text-blue-200 text-sm",children:"No spam, ever. Unsubscribe with one click."})]})})})}},1891:(e,t,a)=>{a.d(t,{default:()=>x});var s=a(687),i=a(3210),r=a(1860),l=a(2941),n=a(9733),o=a(7009),c=a(3936),d=a(5178);let x=()=>{let[e,t]=(0,i.useState)(!1),a=e=>{let a=document.getElementById(e);a&&a.scrollIntoView({behavior:"smooth"}),t(!1)},x=[{label:"Services",href:"#services-overview"},{label:"How It Works",href:"#process"},{label:"About Us",href:"#about"}];return(0,s.jsxs)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300",children:[(0,s.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"ml-2 text-xl font-bold text-dark-charcoal dark:text-white",children:"Mobilify"})]}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[x.map(e=>(0,s.jsx)("button",{onClick:()=>a(e.href.substring(1)),className:"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200",children:e.label},e.label)),(0,s.jsx)(c.ChatTriggers.HeaderChat,{}),(0,s.jsx)(o.default,{children:(0,s.jsx)(d.DarkModeToggles.HeaderToggle,{})}),(0,s.jsx)("button",{onClick:()=>a("contact"),className:"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200",children:"Get a Quote"})]}),(0,s.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,s.jsx)(r.A,{size:24}):(0,s.jsx)(l.A,{size:24})})]})}),(0,s.jsx)(o.default,{children:e&&(0,s.jsx)("div",{className:"md:hidden fixed inset-0 top-16 bg-white z-40",children:(0,s.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[x.map(e=>(0,s.jsx)("button",{onClick:()=>a(e.href.substring(1)),className:"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2",children:e.label},e.label)),(0,s.jsx)("button",{onClick:()=>a("contact"),className:"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6",children:"Get a Quote"})]})})})]})}},3936:(e,t,a)=>{a.d(t,{ChatTriggers:()=>d});var s=a(687);a(3210);var i=a(4709),r=a(8340),l=a(3872),n=a(6001),o=a(8569);let c=({variant:e="button",text:t="Chat with us",icon:a="message",className:c="",size:d="md",context:x="general"})=>{let h=()=>{o.l.setSessionData({trigger_context:x,trigger_page:window.location.pathname,trigger_time:new Date().toISOString()}),o.l.openChat()},u=()=>{let e={className:"sm"===d?"w-4 h-4":"lg"===d?"w-6 h-6":"w-5 h-5"};switch(a){case"help":return(0,s.jsx)(i.A,{...e});case"phone":return(0,s.jsx)(r.A,{...e});default:return(0,s.jsx)(l.A,{...e})}};return"floating"===e?(0,s.jsxs)(n.P.button,{onClick:h,className:`fixed bottom-6 right-6 bg-electric-blue text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-40 ${c}`,whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:2,duration:.3},title:t,children:[u(),(0,s.jsx)("span",{className:"sr-only",children:t})]}):"inline"===e?(0,s.jsxs)("button",{onClick:h,className:`inline-flex items-center gap-2 text-electric-blue hover:text-indigo-700 font-medium transition-colors duration-200 ${c}`,children:[u(),(0,s.jsx)("span",{children:t})]}):(0,s.jsxs)(n.P.button,{onClick:h,className:`inline-flex items-center gap-2 bg-electric-blue text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md hover:shadow-lg ${(()=>{switch(d){case"sm":return"px-3 py-2 text-sm";case"lg":return"px-6 py-4 text-lg";default:return"px-4 py-3 text-base"}})()} ${c}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:[u(),(0,s.jsx)("span",{children:t})]})},d={HeaderChat:()=>(0,s.jsx)(c,{variant:"inline",text:"Live Chat",icon:"message",context:"header",size:"sm"}),ContactChat:()=>(0,s.jsx)(c,{variant:"button",text:"Chat with Our Team",icon:"message",context:"contact",size:"lg",className:"w-full sm:w-auto"}),ServicesChat:()=>(0,s.jsx)(c,{variant:"button",text:"Ask About Pricing",icon:"help",context:"services",size:"md"}),FAQChat:()=>(0,s.jsx)(c,{variant:"inline",text:"Still have questions? Chat with us",icon:"help",context:"faq",size:"md"}),BlogChat:()=>(0,s.jsx)(c,{variant:"inline",text:"Discuss this article",icon:"message",context:"blog",size:"sm"}),FloatingChat:()=>(0,s.jsx)(c,{variant:"floating",text:"Chat with us",icon:"message",context:"floating"}),DemoChat:()=>(0,s.jsx)(c,{variant:"button",text:"Get Help with Demo",icon:"help",context:"demo",size:"md"})}},5178:(e,t,a)=>{a.d(t,{DarkModeToggles:()=>c});var s=a(687);a(3210);var i=a(363),r=a(1134),l=a(6001),n=a(1188);let o=({variant:e="button",size:t="md",className:a="",showLabel:o=!1})=>{let{theme:c,toggleTheme:d}=(0,n.DP)(),x=()=>{switch(t){case"sm":return"w-4 h-4";case"lg":return"w-6 h-6";default:return"w-5 h-5"}};return"switch"===e?(0,s.jsxs)("div",{className:`flex items-center gap-3 ${a}`,children:[o&&(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["dark"===c?"Dark":"Light"," Mode"]}),(0,s.jsx)("button",{onClick:d,className:"relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 dark:focus:ring-offset-gray-800",role:"switch","aria-checked":"dark"===c,"aria-label":`Switch to ${"dark"===c?"light":"dark"} mode`,children:(0,s.jsx)(l.P.span,{className:"inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-200",animate:{x:"dark"===c?24:4},transition:{type:"spring",stiffness:500,damping:30},children:(0,s.jsx)(l.P.div,{className:"flex items-center justify-center h-full w-full",initial:!1,animate:{rotate:180*("dark"===c)},transition:{duration:.2},children:"dark"===c?(0,s.jsx)(i.A,{className:"w-2.5 h-2.5 text-gray-600"}):(0,s.jsx)(r.A,{className:"w-2.5 h-2.5 text-yellow-500"})})})})]}):(0,s.jsxs)("div",{className:`flex items-center gap-2 ${a}`,children:[(0,s.jsx)(l.P.button,{onClick:d,className:`
          ${(()=>{switch(t){case"sm":return"w-8 h-8";case"lg":return"w-12 h-12";default:return"w-10 h-10"}})()}
          flex items-center justify-center
          rounded-lg
          bg-gray-100 hover:bg-gray-200
          dark:bg-gray-800 dark:hover:bg-gray-700
          text-gray-600 dark:text-gray-300
          transition-colors duration-200
          focus:outline-none focus:ring-2 focus:ring-electric-blue focus:ring-offset-2
          dark:focus:ring-offset-gray-800
        `,whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":`Switch to ${"dark"===c?"light":"dark"} mode`,title:`Switch to ${"dark"===c?"light":"dark"} mode`,children:(0,s.jsx)(l.P.div,{initial:!1,animate:{rotate:180*("dark"===c),scale:"dark"===c?.8:1},transition:{duration:.3,ease:"easeInOut"},children:"dark"===c?(0,s.jsx)(r.A,{className:`${x()} text-yellow-400`}):(0,s.jsx)(i.A,{className:`${x()} text-gray-600`})})}),o&&(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["dark"===c?"Light":"Dark"," Mode"]})]})},c={HeaderToggle:()=>(0,s.jsx)(o,{variant:"button",size:"sm",className:"mr-4"}),SettingsToggle:()=>(0,s.jsx)(o,{variant:"switch",size:"md",showLabel:!0,className:"justify-between w-full"}),MobileToggle:()=>(0,s.jsx)(o,{variant:"button",size:"md",showLabel:!0,className:"justify-center"}),FooterToggle:()=>(0,s.jsx)(o,{variant:"switch",size:"sm"})}},9733:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(687);a(3210);let i=({className:e=""})=>(0,s.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})})}};
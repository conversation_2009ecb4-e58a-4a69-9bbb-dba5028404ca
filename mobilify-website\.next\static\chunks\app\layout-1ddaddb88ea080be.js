(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},395:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var i=s(2115);let n=()=>((0,i.useEffect)(()=>{},[]),null)},632:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5302,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,8751)),Promise.resolve().then(s.bind(s,395)),Promise.resolve().then(s.bind(s,4843)),Promise.resolve().then(s.bind(s,7740))},4843:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var i=s(5155),n=s(2115);let r=e=>{let{children:t,fallback:s=null}=e,[r,a]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{a(!0)},[]),r)?(0,i.jsx)(i.Fragment,{children:t}):(0,i.jsx)(i.Fragment,{children:s})}},5302:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},7740:(e,t,s)=>{"use strict";s.d(t,{DP:()=>a,ThemeProvider:()=>o});var i=s(5155),n=s(2115);let r=(0,n.createContext)(void 0),a=()=>{let e=(0,n.useContext)(r);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},o=e=>{let{children:t}=e,[s,a]=(0,n.useState)("light"),[o,c]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";a(e||t),c(!0)},[]),(0,n.useEffect)(()=>{if(!o)return;let e=document.documentElement;"dark"===s?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",s),window.gtag&&window.gtag("event","theme_changed",{event_category:"user_preference",event_label:s})},[s,o]),(0,n.useEffect)(()=>{if(!o)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||a(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[o]),(0,i.jsx)(r.Provider,{value:{theme:s,toggleTheme:()=>{a(e=>"light"===e?"dark":"light")},setTheme:e=>{a(e)}},children:(0,i.jsx)("div",{suppressHydrationWarning:!0,children:t})})}},8751:(e,t,s)=>{"use strict";s.d(t,{default:()=>r,l:()=>a});var i=s(2115),n=s(9509);let r=e=>{let{websiteId:t}=e;return(0,i.useEffect)(()=>{let e=t||n.env.NEXT_PUBLIC_CRISP_WEBSITE_ID;if(!e)return void console.warn("Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable.");if(!window.$crisp){window.$crisp=[],window.CRISP_WEBSITE_ID=e;let t=document.createElement("script");t.src="https://client.crisp.chat/l.js",t.async=!0,document.getElementsByTagName("head")[0].appendChild(t),window.$crisp.push(["safe",!0]),window.$crisp.push(["set","user:company","Mobilify Website Visitor"]),window.$crisp.push(["set","session:data",{source:"website",page:window.location.pathname,timestamp:new Date().toISOString()}]),window.$crisp.push(["on","chat:opened",()=>{window.gtag&&window.gtag("event","chat_opened",{event_category:"engagement",event_label:"crisp_chat"})}]),window.$crisp.push(["on","message:sent",()=>{window.gtag&&window.gtag("event","chat_message_sent",{event_category:"engagement",event_label:"crisp_chat"})}]),window.$crisp.push(["on","message:received",()=>{window.gtag&&window.gtag("event","chat_message_received",{event_category:"engagement",event_label:"crisp_chat"})}])}return()=>{}},[t]),null},a={openChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:open"])},closeChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:close"])},showChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:show"])},hideChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:hide"])},setUser:e=>{window.$crisp&&(e.nickname&&window.$crisp.push(["set","user:nickname",e.nickname]),e.email&&window.$crisp.push(["set","user:email",e.email]),e.phone&&window.$crisp.push(["set","user:phone",e.phone]),e.avatar&&window.$crisp.push(["set","user:avatar",e.avatar]))},sendMessage:e=>{window.$crisp&&window.$crisp.push(["do","message:send",["text",e]])},setSessionData:e=>{window.$crisp&&window.$crisp.push(["set","session:data",e])},isChatAvailable:()=>!!window.$crisp&&window.$crisp.length>0,setSegments:e=>{window.$crisp&&window.$crisp.push(["set","session:segments",e])}}}},e=>{var t=t=>e(e.s=t);e.O(0,[470,441,684,358],()=>t(632)),_N_E=e.O()}]);
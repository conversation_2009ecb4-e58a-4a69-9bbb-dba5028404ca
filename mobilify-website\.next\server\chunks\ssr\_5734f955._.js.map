{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/faq/FAQClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/faq/FAQClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/faq/FAQClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/faq/FAQClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/faq/FAQClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/faq/FAQClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/faq/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { Metada<PERSON> } from 'next';\nimport FAQClient from './FAQClient';\n\nexport const metadata: Metadata = {\n  title: 'FAQ | Mobilify - Frequently Asked Questions',\n  description: 'Find answers to common questions about mobile app development, pricing, process, and more at Mobilify.',\n  openGraph: {\n    title: 'FAQ | Mobilify - Frequently Asked Questions',\n    description: 'Find answers to common questions about mobile app development, pricing, process, and more at Mobilify.',\n    type: 'website',\n  },\n};\n\nexport default function FAQPage() {\n  // For now, we'll use sample data since Sanity isn't set up yet\n  const sampleFAQItems = [\n    {\n      _id: '1',\n      _type: 'faqItem' as const,\n      question: 'How much does it cost to build a mobile app?',\n      answer: [\n        {\n          _type: 'block' as const,\n          _key: '1',\n          children: [\n            {\n              _type: 'span' as const,\n              _key: '1',\n              text: 'Our pricing depends on your specific needs. We offer three main packages: Starter App ($5,000+) for website conversions, Custom App ($15,000+) for new app development, and Enterprise (custom pricing) for complex projects.',\n              marks: []\n            }\n          ]\n        }\n      ],\n      topic: {\n        _id: 'pricing',\n        _type: 'faqTopic' as const,\n        title: 'Pricing',\n        slug: { current: 'pricing' },\n        description: 'Questions about costs and packages'\n      },\n      relatedPosts: [],\n      _createdAt: '2024-01-01',\n      _updatedAt: '2024-01-01'\n    },\n    {\n      _id: '2',\n      _type: 'faqItem' as const,\n      question: 'How long does it take to develop an app?',\n      answer: [\n        {\n          _type: 'block' as const,\n          _key: '1',\n          children: [\n            {\n              _type: 'span' as const,\n              _key: '1',\n              text: 'Timeline varies by project complexity: Simple apps take 4-6 weeks, custom designs take 6-10 weeks, and complex features take 10-16 weeks. We provide detailed timelines during our consultation.',\n              marks: []\n            }\n          ]\n        }\n      ],\n      topic: {\n        _id: 'process',\n        _type: 'faqTopic' as const,\n        title: 'Process',\n        slug: { current: 'process' },\n        description: 'How we work and timelines'\n      },\n      relatedPosts: [],\n      _createdAt: '2024-01-01',\n      _updatedAt: '2024-01-01'\n    },\n    {\n      _id: '3',\n      _type: 'faqItem' as const,\n      question: 'Do you develop for both iOS and Android?',\n      answer: [\n        {\n          _type: 'block' as const,\n          _key: '1',\n          children: [\n            {\n              _type: 'span' as const,\n              _key: '1',\n              text: 'Yes! We develop cross-platform apps that work on both iOS and Android using modern frameworks like React Native. This approach saves time and cost while ensuring your app reaches the widest audience.',\n              marks: []\n            }\n          ]\n        }\n      ],\n      topic: {\n        _id: 'technical',\n        _type: 'faqTopic' as const,\n        title: 'Technical',\n        slug: { current: 'technical' },\n        description: 'Technical questions about development'\n      },\n      relatedPosts: [],\n      _createdAt: '2024-01-01',\n      _updatedAt: '2024-01-01'\n    },\n    {\n      _id: '4',\n      _type: 'faqItem' as const,\n      question: 'Can you convert my existing website into an app?',\n      answer: [\n        {\n          _type: 'block' as const,\n          _key: '1',\n          children: [\n            {\n              _type: 'span' as const,\n              _key: '1',\n              text: 'Absolutely! Our Starter package is specifically designed for website-to-app conversions. We optimize your existing content for mobile and add native app features like push notifications and offline access.',\n              marks: []\n            }\n          ]\n        }\n      ],\n      topic: {\n        _id: 'technical',\n        _type: 'faqTopic' as const,\n        title: 'Technical',\n        slug: { current: 'technical' },\n        description: 'Technical questions about development'\n      },\n      relatedPosts: [],\n      _createdAt: '2024-01-01',\n      _updatedAt: '2024-01-01'\n    },\n    {\n      _id: '5',\n      _type: 'faqItem' as const,\n      question: \"What's included in the Starter package?\",\n      answer: [\n        {\n          _type: 'block' as const,\n          _key: '1',\n          children: [\n            {\n              _type: 'span' as const,\n              _key: '1',\n              text: 'The Starter package includes website conversion, basic mobile optimization, app store submission, push notifications, offline access, and 30 days of support. Perfect for getting your existing business mobile quickly.',\n              marks: []\n            }\n          ]\n        }\n      ],\n      topic: {\n        _id: 'pricing',\n        _type: 'faqTopic' as const,\n        title: 'Pricing',\n        slug: { current: 'pricing' },\n        description: 'Questions about costs and packages'\n      },\n      relatedPosts: [],\n      _createdAt: '2024-01-01',\n      _updatedAt: '2024-01-01'\n    }\n  ];\n\n  return <FAQClient faqItems={sampleFAQItems} />;\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;IACR;AACF;AAEe,SAAS;IACtB,+DAA+D;IAC/D,MAAM,iBAAiB;QACrB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ;gBACN;oBACE,OAAO;oBACP,MAAM;oBACN,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM;4BACN,MAAM;4BACN,OAAO,EAAE;wBACX;qBACD;gBACH;aACD;YACD,OAAO;gBACL,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,MAAM;oBAAE,SAAS;gBAAU;gBAC3B,aAAa;YACf;YACA,cAAc,EAAE;YAChB,YAAY;YACZ,YAAY;QACd;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ;gBACN;oBACE,OAAO;oBACP,MAAM;oBACN,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM;4BACN,MAAM;4BACN,OAAO,EAAE;wBACX;qBACD;gBACH;aACD;YACD,OAAO;gBACL,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,MAAM;oBAAE,SAAS;gBAAU;gBAC3B,aAAa;YACf;YACA,cAAc,EAAE;YAChB,YAAY;YACZ,YAAY;QACd;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ;gBACN;oBACE,OAAO;oBACP,MAAM;oBACN,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM;4BACN,MAAM;4BACN,OAAO,EAAE;wBACX;qBACD;gBACH;aACD;YACD,OAAO;gBACL,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,MAAM;oBAAE,SAAS;gBAAY;gBAC7B,aAAa;YACf;YACA,cAAc,EAAE;YAChB,YAAY;YACZ,YAAY;QACd;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ;gBACN;oBACE,OAAO;oBACP,MAAM;oBACN,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM;4BACN,MAAM;4BACN,OAAO,EAAE;wBACX;qBACD;gBACH;aACD;YACD,OAAO;gBACL,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,MAAM;oBAAE,SAAS;gBAAY;gBAC7B,aAAa;YACf;YACA,cAAc,EAAE;YAChB,YAAY;YACZ,YAAY;QACd;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ;gBACN;oBACE,OAAO;oBACP,MAAM;oBACN,UAAU;wBACR;4BACE,OAAO;4BACP,MAAM;4BACN,MAAM;4BACN,OAAO,EAAE;wBACX;qBACD;gBACH;aACD;YACD,OAAO;gBACL,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,MAAM;oBAAE,SAAS;gBAAU;gBAC3B,aAAa;YACf;YACA,cAAc,EAAE;YAChB,YAAY;YACZ,YAAY;QACd;KACD;IAED,qBAAO,8OAAC,+HAAA,CAAA,UAAS;QAAC,UAAU;;;;;;AAC9B", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAEgG,EAAC;IA2BrHiB,UAAU;;;;;;;;;AAlBZ,OAAO,MAAMd,eAAe,6CAAA;IAC1BC,MAAAA,GAASC;IACTC,EAAAA,OAAAA;IAAAA,CAAWC;IAAAA;QACb,EAAC,UAAA;YAAA;YAAA;gBAED,YAAA;oBAAA,CAAc;oBAAA,+BAA0C;qBAAE,wBAAwB;wBAAuB,UAAA,CAAA;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;oBAEzG;iBAAA,0DAA4D;YAC5D;YAAA,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kBAChDY,QAAAA,CAAAA,CAAY;YAAA;SAAA;;SACVC,MAAMZ,UAAUa,QAAQ;cACxBC,IAAAA,CAAM,CAAA;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;cACNC,OAAAA;YAAAA,EAAU,EAAA;YAAA;SAAA;cACV,OAAA;YAAA,IAAA,6BAA2C;YAAA;SAAA;cAC3CC,UAAAA;YAAAA,CAAY,GAAA;YAAA;SAAA;;OACZC,UAAU;QACVC,MAAAA;IAAAA,GAAU,EAAE;CAAA;;;AAKhB,GAAE,GAAA,uBAAA,sBAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
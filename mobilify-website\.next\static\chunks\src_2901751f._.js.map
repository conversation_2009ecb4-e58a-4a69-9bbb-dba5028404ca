{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/GoogleAnalytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ndeclare global {\n  interface Window {\n    gtag: (...args: any[]) => void;\n    dataLayer: any[];\n  }\n}\n\nconst GoogleAnalytics = () => {\n  useEffect(() => {\n    // Only load Google Analytics in production and if GA_ID is provided\n    const GA_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n\n    if (!GA_ID || process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    // Check if scripts are already loaded\n    if (document.querySelector(`script[src*=\"gtag/js?id=${GA_ID}\"]`)) {\n      return;\n    }\n\n    // Load Google Analytics script\n    const script1 = document.createElement('script');\n    script1.async = true;\n    script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_ID}`;\n    document.head.appendChild(script1);\n\n    const script2 = document.createElement('script');\n    script2.innerHTML = `\n      window.dataLayer = window.dataLayer || [];\n      function gtag(){dataLayer.push(arguments);}\n      gtag('js', new Date());\n      gtag('config', '${GA_ID}');\n    `;\n    document.head.appendChild(script2);\n\n    // Make gtag available globally\n    window.gtag = function() {\n      window.dataLayer = window.dataLayer || [];\n      window.dataLayer.push(arguments);\n    };\n  }, []);\n\n  return null;\n};\n\nexport default GoogleAnalytics;\n"], "names": [], "mappings": ";;;AAckB;AAZlB;;AAFA;;AAWA,MAAM,kBAAkB;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,oEAAoE;YACpE,MAAM;YAEN,wCAAqD;gBACnD;YACF;;YAOA,+BAA+B;YAC/B,MAAM;YAKN,MAAM;QAcR;oCAAG,EAAE;IAEL,OAAO;AACT;GArCM;KAAA;uCAuCS", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/CrispChat.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ndeclare global {\n  interface Window {\n    $crisp: any[];\n    CRISP_WEBSITE_ID: string;\n  }\n}\n\ninterface CrispChatProps {\n  websiteId?: string;\n}\n\nconst CrispChat: React.FC<CrispChatProps> = ({ websiteId }) => {\n  useEffect(() => {\n    // Get website ID from props or environment variable\n    const crispWebsiteId = websiteId || process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID;\n    \n    if (!crispWebsiteId) {\n      console.warn('Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable.');\n      return;\n    }\n\n    // Initialize Crisp if not already loaded\n    if (typeof window !== 'undefined' && !window.$crisp) {\n      window.$crisp = [];\n      window.CRISP_WEBSITE_ID = crispWebsiteId;\n\n      // Create and append the Crisp script\n      const script = document.createElement('script');\n      script.src = 'https://client.crisp.chat/l.js';\n      script.async = true;\n      document.getElementsByTagName('head')[0].appendChild(script);\n\n      // Configure Crisp settings\n      window.$crisp.push(['safe', true]);\n      \n      // Set custom data for better support\n      window.$crisp.push(['set', 'user:company', 'Mobilify Website Visitor']);\n      window.$crisp.push(['set', 'session:data', {\n        source: 'website',\n        page: window.location.pathname,\n        timestamp: new Date().toISOString()\n      }]);\n\n      // Track chat interactions for analytics\n      window.$crisp.push(['on', 'chat:opened', () => {\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'chat_opened', {\n            event_category: 'engagement',\n            event_label: 'crisp_chat'\n          });\n        }\n      }]);\n\n      window.$crisp.push(['on', 'message:sent', () => {\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'chat_message_sent', {\n            event_category: 'engagement',\n            event_label: 'crisp_chat'\n          });\n        }\n      }]);\n\n      window.$crisp.push(['on', 'message:received', () => {\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'chat_message_received', {\n            event_category: 'engagement',\n            event_label: 'crisp_chat'\n          });\n        }\n      }]);\n    }\n\n    // Cleanup function\n    return () => {\n      // Note: Crisp doesn't provide a clean way to remove itself\n      // This is intentional as chat sessions should persist across page navigation\n    };\n  }, [websiteId]);\n\n  // This component doesn't render anything visible\n  // The chat widget is injected by Crisp's script\n  return null;\n};\n\nexport default CrispChat;\n\n// Utility functions for programmatic chat control\nexport const crispUtils = {\n  // Open the chat widget\n  openChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:open']);\n    }\n  },\n\n  // Close the chat widget\n  closeChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:close']);\n    }\n  },\n\n  // Show the chat widget\n  showChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:show']);\n    }\n  },\n\n  // Hide the chat widget\n  hideChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:hide']);\n    }\n  },\n\n  // Set user information\n  setUser: (user: { nickname?: string; email?: string; phone?: string; avatar?: string }) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      if (user.nickname) window.$crisp.push(['set', 'user:nickname', user.nickname]);\n      if (user.email) window.$crisp.push(['set', 'user:email', user.email]);\n      if (user.phone) window.$crisp.push(['set', 'user:phone', user.phone]);\n      if (user.avatar) window.$crisp.push(['set', 'user:avatar', user.avatar]);\n    }\n  },\n\n  // Send a message programmatically\n  sendMessage: (message: string) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'message:send', ['text', message]]);\n    }\n  },\n\n  // Set session data\n  setSessionData: (data: Record<string, any>) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['set', 'session:data', data]);\n    }\n  },\n\n  // Check if chat is available\n  isChatAvailable: (): boolean => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      return window.$crisp.length > 0;\n    }\n    return false;\n  },\n\n  // Set custom segments for targeting\n  setSegments: (segments: string[]) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['set', 'session:segments', segments]);\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;AAkBwC;AAhBxC;;AAFA;;AAeA,MAAM,YAAsC,CAAC,EAAE,SAAS,EAAE;;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,oDAAoD;YACpD,MAAM,iBAAiB,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,4BAA4B;YAE5E,IAAI,CAAC,gBAAgB;gBACnB,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,yCAAyC;YACzC,IAAI,aAAkB,eAAe,CAAC,OAAO,MAAM,EAAE;gBACnD,OAAO,MAAM,GAAG,EAAE;gBAClB,OAAO,gBAAgB,GAAG;gBAE1B,qCAAqC;gBACrC,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,OAAO,GAAG,GAAG;gBACb,OAAO,KAAK,GAAG;gBACf,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC;gBAErD,2BAA2B;gBAC3B,OAAO,MAAM,CAAC,IAAI,CAAC;oBAAC;oBAAQ;iBAAK;gBAEjC,qCAAqC;gBACrC,OAAO,MAAM,CAAC,IAAI,CAAC;oBAAC;oBAAO;oBAAgB;iBAA2B;gBACtE,OAAO,MAAM,CAAC,IAAI,CAAC;oBAAC;oBAAO;oBAAgB;wBACzC,QAAQ;wBACR,MAAM,OAAO,QAAQ,CAAC,QAAQ;wBAC9B,WAAW,IAAI,OAAO,WAAW;oBACnC;iBAAE;gBAEF,wCAAwC;gBACxC,OAAO,MAAM,CAAC,IAAI,CAAC;oBAAC;oBAAM;;+CAAe;4BACvC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;gCACxD,OAAe,IAAI,CAAC,SAAS,eAAe;oCAC3C,gBAAgB;oCAChB,aAAa;gCACf;4BACF;wBACF;;iBAAE;gBAEF,OAAO,MAAM,CAAC,IAAI,CAAC;oBAAC;oBAAM;;+CAAgB;4BACxC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;gCACxD,OAAe,IAAI,CAAC,SAAS,qBAAqB;oCACjD,gBAAgB;oCAChB,aAAa;gCACf;4BACF;wBACF;;iBAAE;gBAEF,OAAO,MAAM,CAAC,IAAI,CAAC;oBAAC;oBAAM;;+CAAoB;4BAC5C,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;gCACxD,OAAe,IAAI,CAAC,SAAS,yBAAyB;oCACrD,gBAAgB;oCAChB,aAAa;gCACf;4BACF;wBACF;;iBAAE;YACJ;YAEA,mBAAmB;YACnB;uCAAO;gBACL,2DAA2D;gBAC3D,6EAA6E;gBAC/E;;QACF;8BAAG;QAAC;KAAU;IAEd,iDAAiD;IACjD,gDAAgD;IAChD,OAAO;AACT;GAvEM;KAAA;uCAyES;AAGR,MAAM,aAAa;IACxB,uBAAuB;IACvB,UAAU;QACR,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAY;QACxC;IACF;IAEA,wBAAwB;IACxB,WAAW;QACT,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAa;QACzC;IACF;IAEA,uBAAuB;IACvB,UAAU;QACR,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAY;QACxC;IACF;IAEA,uBAAuB;IACvB,UAAU;QACR,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAY;QACxC;IACF;IAEA,uBAAuB;IACvB,SAAS,CAAC;QACR,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,IAAI,KAAK,QAAQ,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAO;gBAAiB,KAAK,QAAQ;aAAC;YAC7E,IAAI,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAO;gBAAc,KAAK,KAAK;aAAC;YACpE,IAAI,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAO;gBAAc,KAAK,KAAK;aAAC;YACpE,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAO;gBAAe,KAAK,MAAM;aAAC;QACzE;IACF;IAEA,kCAAkC;IAClC,aAAa,CAAC;QACZ,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;gBAAgB;oBAAC;oBAAQ;iBAAQ;aAAC;QAC9D;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC;QACf,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAO;gBAAgB;aAAK;QAClD;IACF;IAEA,6BAA6B;IAC7B,iBAAiB;QACf,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,OAAO,MAAM,CAAC,MAAM,GAAG;QAChC;QACA,OAAO;IACT;IAEA,oCAAoC;IACpC,aAAa,CAAC;QACZ,IAAI,aAAkB,eAAe,OAAO,MAAM,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAO;gBAAoB;aAAS;QAC1D;IACF;AACF", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [theme, setThemeState] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('mobilify-theme') as Theme;\n    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    const initialTheme = savedTheme || systemTheme;\n    \n    setThemeState(initialTheme);\n    setMounted(true);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = document.documentElement;\n    \n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n\n    // Save to localStorage\n    localStorage.setItem('mobilify-theme', theme);\n\n    // Track theme change for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'theme_changed', {\n        event_category: 'user_preference',\n        event_label: theme\n      });\n    }\n  }, [theme, mounted]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    if (!mounted) return;\n\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = (e: MediaQueryListEvent) => {\n      // Only auto-switch if user hasn't manually set a preference\n      const savedTheme = localStorage.getItem('mobilify-theme');\n      if (!savedTheme) {\n        setThemeState(e.matches ? 'dark' : 'light');\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [mounted]);\n\n  const toggleTheme = () => {\n    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>\n      <div suppressHydrationWarning>\n        {children}\n      </div>\n    </ThemeContext.Provider>\n  );\n};\n\n// Hook for getting system theme preference\nexport const useSystemTheme = (): Theme => {\n  const [systemTheme, setSystemTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setSystemTheme(e.matches ? 'dark' : 'light');\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  return systemTheme;\n};\n\n// Utility function to get theme-aware classes\nexport const getThemeClasses = (lightClasses: string, darkClasses: string) => {\n  return `${lightClasses} dark:${darkClasses}`;\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;;;AAFA;;AAYA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;;IACtE,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;YACzF,MAAM,eAAe,cAAc;YAEnC,cAAc;YACd,WAAW;QACb;kCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,OAAO,SAAS,eAAe;YAErC,IAAI,UAAU,QAAQ;gBACpB,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;YAEA,uBAAuB;YACvB,aAAa,OAAO,CAAC,kBAAkB;YAEvC,mCAAmC;YACnC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;gBACxD,OAAe,IAAI,CAAC,SAAS,iBAAiB;oBAC7C,gBAAgB;oBAChB,aAAa;gBACf;YACF;QACF;kCAAG;QAAC;QAAO;KAAQ;IAEnB,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM;wDAAe,CAAC;oBACpB,4DAA4D;oBAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;oBACxC,IAAI,CAAC,YAAY;wBACf,cAAc,EAAE,OAAO,GAAG,SAAS;oBACrC;gBACF;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;2CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;kCAAG;QAAC;KAAQ;IAEZ,MAAM,cAAc;QAClB,cAAc,CAAA,YAAa,cAAc,UAAU,SAAS;IAC9D;IAEA,MAAM,WAAW,CAAC;QAChB,cAAc;IAChB;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAa;QAAS;kBAC3D,cAAA,6LAAC;YAAI,wBAAwB;sBAC1B;;;;;;;;;;;AAIT;IAvEa;KAAA;AA0EN,MAAM,iBAAiB;;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,eAAe,WAAW,OAAO,GAAG,SAAS;YAE7C,MAAM;yDAAe,CAAC;oBACpB,eAAe,EAAE,OAAO,GAAG,SAAS;gBACtC;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;4CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;mCAAG,EAAE;IAEL,OAAO;AACT;IAhBa;AAmBN,MAAM,kBAAkB,CAAC,cAAsB;IACpD,OAAO,GAAG,aAAa,MAAM,EAAE,aAAa;AAC9C", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/NoSSR.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface NoSSRProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\nconst NoSSR = ({ children, fallback = null }: NoSSRProps) => {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return <>{fallback}</>;\n  }\n\n  return <>{children}</>;\n};\n\nexport default NoSSR;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASA,MAAM,QAAQ,CAAC,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,WAAW;QACb;0BAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZM;KAAA;uCAcS", "debugId": null}}]}
[{"name": "generate-buildid", "duration": 286, "timestamp": 16546555146, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751512019759, "traceId": "3d796b588560997b"}, {"name": "load-custom-routes", "duration": 383, "timestamp": 16546555559, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751512019759, "traceId": "3d796b588560997b"}, {"name": "create-dist-dir", "duration": 562, "timestamp": 16546660805, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751512019865, "traceId": "3d796b588560997b"}, {"name": "create-pages-mapping", "duration": 325, "timestamp": 16546826213, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751512020030, "traceId": "3d796b588560997b"}, {"name": "collect-app-paths", "duration": 3232, "timestamp": 16546826610, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751512020030, "traceId": "3d796b588560997b"}, {"name": "create-app-mapping", "duration": 4572, "timestamp": 16546829896, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751512020034, "traceId": "3d796b588560997b"}, {"name": "public-dir-conflict-check", "duration": 922, "timestamp": 16546835111, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751512020039, "traceId": "3d796b588560997b"}, {"name": "generate-routes-manifest", "duration": 3714, "timestamp": 16546836311, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751512020040, "traceId": "3d796b588560997b"}, {"name": "next-build", "duration": 9096631, "timestamp": 16546405883, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.4", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1751512019610, "traceId": "3d796b588560997b"}]